import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/NotaryOfficeInformation.dart';
import 'package:bloc_test/models/NotaryOfficeItem.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/amap_location_utils.dart';
import 'package:bloc_test/utils/common_tools.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:permission_handler/permission_handler.dart';

import '../widgets/AlertView.dart';
import 'choose_notary_item_event.dart';
import 'choose_notary_item_state.dart';

class ChooseNotaryItemBloc extends Bloc<ChooseNotaryItemEvent, ChooseNotaryItemState> {
  ChooseNotaryItemBloc() : super(ChooseNotaryItemState().init()) {
    on<InitEvent>(_init);
    on<ChooseNotaryOfficeItemEvent>(_chooseNotaryOfficeItem);
    on<ChooseCityEvent>(_chooseCityEvent);
    on<ChooseNotarySingleModelItemEvent>(_chooseNotarySingleModelItemEvent);
    on<ChooseNotaryOfficeSingleModelChildItemEvent>(_chooseNotaryOfficeSingleModelChildItemEvent);
    on<ShowChildrenListEvent>(_showChildrenListEvent);
    on<ChooseCitySuccessEvent>(_chooseCitySuccessEvent);
    on<ChooseNotarySuccessEvent>(_chooseNotarySuccessEvent);
    on<JumpIntoOtherPageEvent>(_jumpIntoOtherPageEvent);
  }

  void _init(InitEvent event, Emitter<ChooseNotaryItemState> emit) async {
     getLocationRequest();
     List<NotaryOfficeItem> tempList = await getNotarizationPurpose();
     state.notaryPurposeList.clear();
     emit(state.clone()..notaryPurposeList = tempList);
  }
  void _chooseNotaryOfficeItem(ChooseNotaryOfficeItemEvent event, Emitter<ChooseNotaryItemState> emit) async {
    // showNotaryOfficeListAlert(AppApplication.currentContext!);
   List<NotaryOfficeInformation> tempList = await getNotarialOfficeList(state.selectedCityId);
   emit(state.clone()..notaryCategories = tempList);
   List<String> notaryNameList = [];
   for (var item in tempList) {
     notaryNameList.add(item.notarialName?? '');
   }
   showNotaryOfficeListAlert(AppApplication.currentContext!,notaryNameList);
  }
  void _chooseCityEvent(ChooseCityEvent event, Emitter<ChooseNotaryItemState> emit) async {
    showCityPickerView(AppApplication.currentContext!, resultCallBack: (CityResult result){
     add(ChooseCitySuccessEvent("${result.province ?? ''} ${result.city ?? ''}",result.cityCode??''));
    }, locationCode: state.selectedCityId);
  }

  void _chooseNotarySingleModelItemEvent(ChooseNotarySingleModelItemEvent event, Emitter<ChooseNotaryItemState> emit) async {
    emit(state.clone()..notaryOfficeItem = event.notarySingleModel);
    add(ShowChildrenListEvent(state.notaryOfficeItem!.children!));
  }

  void _chooseNotaryOfficeSingleModelChildItemEvent(ChooseNotaryOfficeSingleModelChildItemEvent event, Emitter<ChooseNotaryItemState> emit) async {
    if (state.selectedChildren.isEmpty) {
      emit(state.clone()..selectedChildren.add(event.children));
    } else {
     if (state.selectedChildren.contains(event.children)){
       emit(state.clone()..selectedChildren.remove(event.children));
     } else {
       emit(state.clone()..selectedChildren.add(event.children));
     }

    }

  }

  void _showChildrenListEvent(ShowChildrenListEvent event, Emitter<ChooseNotaryItemState> emit) async {
    emit(state.clone()..childrenList = event.childrenList);
  }

  void _chooseCitySuccessEvent(ChooseCitySuccessEvent event, Emitter<ChooseNotaryItemState> emit) async {
    emit(state.clone()..selectedCity = event.city
    ..selectedCityId = event.cityCode);
  }

  void _chooseNotarySuccessEvent(ChooseNotarySuccessEvent event, Emitter<ChooseNotaryItemState> emit) async {
    emit(state.clone()..selectedNotary = event.notary
    ..selectedNotaryId = event.notaryCode);
  }

  void _jumpIntoOtherPageEvent(JumpIntoOtherPageEvent event, Emitter<ChooseNotaryItemState> emit) async {
    AppApplication.getCurrentState()?.pushNamed(RoutePaths.applyInformation,arguments: {
      "notarialName": {
        "notarialName":state.selectedNotary,
        "unitGuid":state.selectedNotaryId
      },
      "selectDan": state.notaryOfficeItem?.unitGuid,
      "selectDuo": state.selectedChildren,
      "purposeName": state.notaryOfficeItem?.name,
      "isAgent": event.isAgent
    });
  }

  getLocationRequest() async {
    if(!await Permission.location.status.isGranted) {
      CommonTools.showCustomToast(context: AppApplication.currentContext!, titleText: "定位权限使用说明：", subTitleText: "用于获取当前位置信息", time: 2);
    }
    if (await Permission.location.request().isGranted) {
      AmapLocationUtils.getStart((result){
        logger.d("获取的定位信息--------$result");
       // final city = "${result['province']} ${result['city']}";
       // final adCode = "${result['adCode'].substring(0, 4)}00";
       // final latLng = "${location.latLng.longitude},${location.latLng.latitude}";
      },errorCallBack: (error){
        ToastUtil.showErrorToast("无法获取当前定位信息");
      });
    } else {
      CommonTools.showPermissionDialog(str: "访问定位权限");
    }
  }
 Future<List<NotaryOfficeInformation>> getNotarialOfficeList(String adCode) async {
    state.notaryCategories.clear();
    Map<String,dynamic> params = {
      "institutionType": 1,
      "cityCode": adCode,
    };
    final response = await ApiInstance().post("${StrUtil.notaryModule}/cgz/notarialOffice/selectAll",queryParameters: params,errorFunction: (){

    });
    if(response != null && response["code"] == 200) {
      List<dynamic> list = response["items"];
      List<NotaryOfficeInformation> tempList = [];
      for (var element in list) {
        tempList.add(NotaryOfficeInformation.fromJson(element));
      }
      return tempList;
    } else {
      return [];
    }
  }

  // 选择公证处弹窗
  void showNotaryOfficeListAlert(BuildContext context,List<String> notaryName) {
    showDialog(
        context: context,
        useSafeArea: false,
        builder: (context) {
          return BottomAlertSearchList(
            holderString: "请输入你要搜索的公证处",
            selectValueCallBack: (value) {
              for (var element in state.notaryCategories) {
                if (element.notarialName == value) {
                  add(ChooseNotarySuccessEvent(value, element.unitGuid??''));
                }
              }
            },
            dataSource:  notaryName,
          );
        });
  }

  Future<List<NotaryOfficeItem>> getNotarizationPurpose() async {
    //获取公证用途列表
    EasyLoading.show();
    final response = await ApiInstance().post("${StrUtil.notaryModule}/cgz/notarypurpose/selectTree",data: {},errorFunction: (error){
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    });
    if(response != null && response["code"] == 200) {
      EasyLoading.dismiss();
      logger.d("获取公证处事项列表----${response["items"]}");
      List tempList = response["items"];
      List<NotaryOfficeItem> list = [];
      for (var element in tempList) {
        list.add(NotaryOfficeItem.fromJson(element));
      }
      return list;
    } else {
      return [];
    }
  }
}
