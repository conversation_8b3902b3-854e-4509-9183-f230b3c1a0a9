import 'package:bloc_test/models/NotaryOfficeItem.dart';

abstract class ChooseNotaryItemEvent {}

class InitEvent extends ChooseNotaryItemEvent {}

class ChooseCityEvent extends ChooseNotaryItemEvent {
  final String cityCode;
  ChooseCityEvent(this.cityCode);
}

class ChooseNotaryOfficeItemEvent extends ChooseNotaryItemEvent {
  ChooseNotaryOfficeItemEvent();
}

class ChooseNotarySingleModelItemEvent extends ChooseNotaryItemEvent {
  final NotaryOfficeItem notarySingleModel;
  ChooseNotarySingleModelItemEvent(this.notarySingleModel);
}

class ChooseNotaryOfficeSingleModelChildItemEvent extends ChooseNotaryItemEvent {
  final Children children;
  ChooseNotaryOfficeSingleModelChildItemEvent(this.children);
}

class ShowChildrenListEvent extends ChooseNotaryItemEvent {
  final List<Children> childrenList;
  ShowChildrenListEvent(this.childrenList);
}

class ChooseCitySuccessEvent extends ChooseNotaryItemEvent {
  final String city;
  final String cityCode;
  ChooseCitySuccessEvent(this.city,this.cityCode);
}

class ChooseNotarySuccessEvent extends ChooseNotaryItemEvent {
  final String notary;
  final String notaryCode;
  ChooseNotarySuccessEvent(this.notary,this.notaryCode);
}

class JumpIntoOtherPageEvent extends ChooseNotaryItemEvent {
  final int isAgent;
  JumpIntoOtherPageEvent(this.isAgent);
}