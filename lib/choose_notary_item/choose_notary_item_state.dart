import 'package:bloc_test/models/NotaryOfficeInformation.dart';
import 'package:bloc_test/models/NotaryOfficeItem.dart';

class ChooseNotaryItemState {

  String selectedCity = '请点击选择';
  String selectedNotary = '请点击选择';
  String selectedCityId = '320100';
  String selectedNotaryId = '';
  List<String> selectedCategories = [];
  List<NotaryOfficeInformation> notaryCategories = [];
  NotaryOfficeItem? notaryOfficeItem ;
  List<NotaryOfficeItem> notaryPurposeList = [];
  List<Children> selectedChildren = [];
  List<Children> childrenList = [];

  ChooseNotaryItemState init() {
    return ChooseNotaryItemState()
    ..selectedCity = '请点击选择'
    ..selectedCityId = ''
    ..selectedNotary = '请点击选择'
      ..notaryCategories = []
      ..selectedCategories = []
      ..notaryPurposeList = []
      ..notaryOfficeItem = null
      ..selectedChildren = []
      ..childrenList = []
    ..selectedNotaryId = '';
  }

  ChooseNotaryItemState clone() {
    return ChooseNotaryItemState()
    ..selectedNotary = selectedNotary
    ..selectedNotaryId = selectedNotaryId
    ..selectedCity = selectedCity
      ..selectedCategories = selectedCategories
      ..notaryCategories = notaryCategories
      ..notaryPurposeList = notaryPurposeList
      ..notaryOfficeItem = notaryOfficeItem
      ..selectedChildren = selectedChildren
      ..childrenList = childrenList
    ..selectedCityId = selectedCityId;
  }

  bool get isValid => selectedCityId.isNotEmpty && selectedNotaryId.isNotEmpty && notaryOfficeItem != null && selectedChildren.isNotEmpty;
}
