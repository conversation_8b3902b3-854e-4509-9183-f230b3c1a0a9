import 'package:bloc_test/utils/appTheme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'choose_notary_item_bloc.dart';
import 'choose_notary_item_event.dart';
import 'choose_notary_item_state.dart';

class ChooseNotaryItemPage extends StatelessWidget {
  final int isAgent;
  const ChooseNotaryItemPage({super.key, required this.isAgent});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (BuildContext context) => ChooseNotaryItemBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final block = context.read<ChooseNotaryItemBloc>();
    return BlocBuilder<ChooseNotaryItemBloc, ChooseNotaryItemState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: const Color(0xFFF5F7FA),
          appBar: _buildAppBar(),
          body: Column(
            children: [
              // 步骤指示器
              Container(
                color: AppTheme.white,
                width: double.infinity,
                child: Image.asset("images/stepOne.png", fit: BoxFit.fill),
              ),
              // 主要内容
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildOfficeSection(),
                      SizedBox(height: 24.h),
                      _buildCitySection(state, block, context),
                      SizedBox(height: 24.h),
                      _buildNotaryOfficeSection(block, state, context),
                      SizedBox(height: 24.h),
                      _buildPurposeSection(block, state),
                      state.childrenList.isEmpty
                          ? SizedBox()
                          : _buildCategorySection(block, state),
                      SizedBox(height: 80.h), // 为底部按钮留空间
                    ],
                  ),
                ),
              ),
            ],
          ),
          bottomNavigationBar: _buildBottomButton(block, state),
        );
      },
    );
  }

  /// 构建AppBar
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        '选择公证事项',
        style: TextStyle(
          color: Colors.white,
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// 构建办理机构部分
  Widget _buildOfficeSection() {
    return Text(
      '办理机构',
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.bold,
        color: const Color(0xFF333333),
      ),
    );
  }

  /// 构建当前城市选择
  Widget _buildCitySection(
    ChooseNotaryItemState state,
    ChooseNotaryItemBloc block,
    BuildContext context,
  ) {
    return GestureDetector(
      onTap: () {
        block.add(ChooseCityEvent(""));
      },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: AppTheme.colorD9D9D9.withAlpha(50),
              blurRadius: 4,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '当前城市',
              style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333)),
            ),
            Row(
              children: [
                Text(
                  state.selectedCity,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: state.selectedCity == "请点击选择" ? const Color(0xFF999999)
                        : const Color(0xFF666666),
                  ),
                ),
                SizedBox(width: 8.w),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建公证处选择
  Widget _buildNotaryOfficeSection(
    ChooseNotaryItemBloc block,
    ChooseNotaryItemState state,
    BuildContext context,
  ) {
    return GestureDetector(
      onTap: () {
        block.add(ChooseNotaryOfficeItemEvent());
      },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: AppTheme.colorD9D9D9.withAlpha(50),
              blurRadius: 4,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '公证处',
              style: TextStyle(fontSize: 14.sp, color: const Color(0xFF333333)),
            ),
            Row(
              children: [
                Text(
                  state.selectedNotary,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color:
                        state.selectedNotary == "请点击选择"
                            ? const Color(0xFF999999)
                            : const Color(0xFF666666),
                  ),
                ),
                SizedBox(width: 8.w),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建公证用途分类选择
  Widget _buildPurposeSection(
    ChooseNotaryItemBloc block,
    ChooseNotaryItemState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '按公证用途分类（单选）',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 16.h),
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children:
              state.notaryPurposeList.map((category) {
                final isSelected = state.notaryOfficeItem?.unitGuid == category.unitGuid;
                return GestureDetector(
                  onTap: () {
                    block.add(ChooseNotarySingleModelItemEvent(category));
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 8.h,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? AppTheme.themeBlue
                              : const Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(20.r),
                      border: Border.all(
                        color:
                            isSelected
                                ? AppTheme.themeBlue
                                : const Color(0xFFE0E0E0),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      category.name??'',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color:
                            isSelected ? Colors.white : const Color(0xFF666666),
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildCategorySection(
      ChooseNotaryItemBloc block,
      ChooseNotaryItemState state,
      ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 16.h),
        Text(
          '按公证事项分类（多选）',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 16.h),
        Wrap(
          spacing: 12.w,
          runSpacing: 12.h,
          children:
          state.childrenList.isEmpty ? [] : state.childrenList.map((category) {
            final isSelected = state.selectedChildren.contains(category);
            return GestureDetector(
              onTap: () {
                block.add(ChooseNotaryOfficeSingleModelChildItemEvent(category));
              },
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 8.h,
                ),
                decoration: BoxDecoration(
                  color:
                  isSelected
                      ? AppTheme.themeBlue
                      : const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(
                    color:
                    isSelected
                        ? AppTheme.themeBlue
                        : const Color(0xFFE0E0E0),
                    width: 1,
                  ),
                ),
                child: Text(
                  category.name??'',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color:
                    isSelected ? Colors.white : const Color(0xFF666666),
                    fontWeight:
                    isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButton(
    ChooseNotaryItemBloc block,
    ChooseNotaryItemState state,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed:
                state.isValid ? (){
              block.add(JumpIntoOtherPageEvent(isAgent));
                } : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.themeBlue,
              foregroundColor: Colors.white,
              disabledBackgroundColor: const Color(0xFFE0E0E0),
              disabledForegroundColor: const Color(0xFF9E9E9E),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              elevation: 0,
            ),
            child: Text(
              '下一步',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }

}
