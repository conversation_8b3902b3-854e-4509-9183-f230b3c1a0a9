abstract class LoginPageEvent {}

class LoginAccountChanged extends LoginPageEvent {
  final String account;
  LoginAccountChanged(this.account);
}

class LoginPasswordChanged extends LoginPageEvent {
  final String password;
  LoginPasswordChanged(this.password);
}

class LoginPasswordVisibilityToggled extends LoginPageEvent {}

class LoginPrivacyCheckedChanged extends LoginPageEvent {
  final bool checked;
  LoginPrivacyCheckedChanged(this.checked);
}

class LoginSubmitted extends LoginPageEvent {}

class WeChatLoginSubmitted extends LoginPageEvent {}

class AppleLoginSubmitted extends LoginPageEvent {}