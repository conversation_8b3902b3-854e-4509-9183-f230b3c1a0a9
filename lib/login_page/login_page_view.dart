import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'login_page_bloc.dart';
import 'login_page_event.dart';
import 'login_page_state.dart';

class LoginPageView extends StatelessWidget {
  const LoginPageView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => LoginPageBloc(),
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          leading: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(Icons.arrow_back_ios_new, color: AppTheme.textBlack),
          ),
        ),
        body: Builder(
          builder: (context) {
            final bloc = context.read<LoginPageBloc>();
            return Stack(
              alignment: Alignment.bottomCenter,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  height: double.infinity,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // 插画图片
                        Image.asset(
                          'images/logoimg.png', // 替换为你的插画图片路径
                          height: 180.h,
                        ),
                        SizedBox(height: 20.h),
                        _buildInputField(
                          hint: "请输入账号",
                          value: "账号",
                          onChanged: (value) {
                            bloc.add(LoginAccountChanged(value));
                          },
                        ),
                        SizedBox(height: 20.h),
                        _buildPasswordField(
                          label: "密码",
                          hint: "请输入密码",
                          onChanged: (value) {
                            bloc.add(LoginPasswordChanged(value));
                          },
                          onToggleVisibility: () {
                            bloc.add(LoginPasswordVisibilityToggled());
                          },
                        ),
                        SizedBox(height: 20.h),
                        // 验证码登录/忘记密码
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextButton(
                              onPressed: () {
                                // 跳转验证码登录
                                AppApplication.getCurrentState()?.pushNamed(
                                  RoutePaths.codeLogin,
                                );
                              },
                              child: Text('验证码登录'),
                            ),
                            TextButton(
                              onPressed: () {
                                // 跳转忘记密码
                                AppApplication.getCurrentState()?.pushNamed(
                                  RoutePaths.modifyPsd,
                                  arguments: "找回密码",
                                );
                              },
                              child: Text('忘记密码?'),
                            ),
                          ],
                        ),
                        // 登录按钮
                        BlocBuilder<LoginPageBloc, LoginPageState>(
                          builder: (context, state) {
                            return SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                onPressed:
                                state.canLogin
                                    ? () => bloc.add(LoginSubmitted())
                                    : null,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: AppTheme.themeBlue,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.r),
                                  ),
                                ),
                                child: Text(
                                  '立即登录',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            );
                          },
                        ),
                        SizedBox(height: 16.h),
                        // 注册
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text('没有账号？'),
                            TextButton(
                              onPressed: () {
                                // 跳转注册
                                AppApplication.getCurrentState()?.pushNamed(
                                  RoutePaths.register,
                                  arguments: {
                                    'unionId': "",
                                    'appleId': "",
                                    'mobile': "",
                                    'loginType': 3,
                                  },
                                );
                              },
                              child: Text('立即注册'),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                        // 微信/Apple 登录
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            IconButton(
                              icon: Image.asset(
                                'images/icon_wechat.png',
                                width: 40.w,
                              ),
                              onPressed: () {
                                bloc.add(WeChatLoginSubmitted());
                              },
                            ),
                            SizedBox(width: 32.w),
                            IconButton(
                              icon: Image.asset(
                                'images/icon_apple.png',
                                width: 40.w,
                              ),
                              onPressed: () {
                                // Apple 登录
                                bloc.add(AppleLoginSubmitted());
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                SafeArea(
                  child: BlocBuilder<LoginPageBloc, LoginPageState>(
                    builder: (context, state) {
                      return Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30.w),
                        child: Row(
                          children: [
                            Checkbox(
                              value: state.privacyChecked,
                              onChanged:
                                  (value) =>
                                  bloc.add(LoginPrivacyCheckedChanged(value!)),
                            ),
                            Expanded(
                              child: RichText(
                                text: TextSpan(
                                  text: "登录即代表您已阅读并同意",
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.black,
                                  ),
                                  children: [
                                    TextSpan(
                                      text: "《青桐智盒隐私政策》",
                                      style: TextStyle(
                                          color: AppTheme.themeBlue),
                                      recognizer:
                                      TapGestureRecognizer()
                                        ..onTap = () {
                                          AppApplication.getCurrentState()
                                              ?.pushNamed(
                                            RoutePaths.webViewWidget,
                                            arguments: {
                                              'title': "青桐智盒隐私政策",
                                              'url': StrUtil.privacyPolicy,
                                            },
                                          );
                                        },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildInputField({
    required String hint,
    required String value,
    required Function(String) onChanged,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              "账号",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            keyboardType: keyboardType,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14.sp,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              onChanged(value);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordField({
    required String label,
    required String hint,
    required Function(String) onChanged,
    required VoidCallback onToggleVisibility,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: BlocBuilder<LoginPageBloc, LoginPageState>(
            builder: (context, state) {
              return TextField(
                keyboardType: TextInputType.visiblePassword,
                obscureText:!state.showPassword,
                decoration: InputDecoration(
                  hintText: hint,
                  hintStyle: TextStyle(
                      color: Colors.grey.shade400, fontSize: 14),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  suffixIcon: IconButton(
                    onPressed: onToggleVisibility,
                    icon: Icon(
                      state.showPassword ? Icons.visibility : Icons
                          .visibility_off,
                      color: AppTheme.textBlack_1,
                      size: 20.sp,
                    ),
                  ),
                ),
                onChanged: (value) {
                  onChanged(value);
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
