class LoginPageState {
  final String account;
  final String password;
  final bool showPassword;
  final bool privacyChecked;

  bool get canLogin => account.isNotEmpty && password.isNotEmpty && privacyChecked;

  LoginPageState({
    this.account = '',
    this.password = '',
    this.showPassword = false,
    this.privacyChecked = false,
  });

  LoginPageState copyWith({
    String? account,
    String? password,
    bool? showPassword,
    bool? privacyChecked,
    bool? isVisible,
  }) {
    return LoginPageState(
      account: account ?? this.account,
      password: password ?? this.password,
      showPassword: showPassword ?? this.showPassword,
      privacyChecked: privacyChecked ?? this.privacyChecked,
    );
  }
}