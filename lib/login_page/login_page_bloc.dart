
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/userInfo.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluwx/fluwx.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../utils/router.dart';
import '../utils/toast_util.dart';
import 'login_page_event.dart';
import 'login_page_state.dart';

class LoginPageBloc extends Bloc<LoginPageEvent, LoginPageState> {

  UserInfoEntity? userInfoEntity;
  // 0: 本机号码一键登录 1：微信登录 2：Apple登录 3:直接注册
  int loginType = 0;

  // 手机号
  String mobile = '';

  // 微信unionId
  String unionId =  '';

  // 苹果账号
  String appleId = '';

  LoginPageBloc() : super(LoginPageState()) {
    on<LoginAccountChanged>((event, emit) {
      emit(state.copyWith(account: event.account));
    });
    on<LoginPasswordChanged>((event, emit) {
      emit(state.copyWith(password: event.password));
    });
    on<LoginPasswordVisibilityToggled>((event, emit) {
      emit(state.copyWith(showPassword: !state.showPassword));
    });
    on<LoginPrivacyCheckedChanged>((event, emit) async{
      emit(state.copyWith(privacyChecked: event.checked));
      SpUtil.instance.setBool(StrUtil.isAgree, event.checked);
    });
    on<LoginSubmitted>((event, emit) {
      login();
    });
    on<WeChatLoginSubmitted>((event, emit){
      launchWithWeChat();
    });
    on<AppleLoginSubmitted>((event, emit){
      appleLogin();
    });
  }

  login() {
    if (state.account.isEmpty) {
      ToastUtil.showWarningToast("用户名或密码不能为空！");
      return;
    }
    if(!state.privacyChecked){
      ToastUtil.showWarningToast("请先同意隐私协议");
      return;
    }
    Map<String, String> map = {
      "loginName": state.account,
      "password": generateMd5(state.password),
      "source": "1",
      "type": "2"
    };
    EasyLoading.show(status: "登录中...");
    ApiInstance().post("${StrUtil.userModule}/sys/login/loginOn",data: map,errorFunction: (error){
      EasyLoading.dismiss();
    }).then((response){
       if(response["code"] == 200 && response["items"]["role"].length > 0) {
         SpUtil.instance.remove(StrUtil.userInfo);
         Map<String,String> params = {
           "roleId": response["items"]["role"][0]["roleId"],
           "device":'app'
         };
         getUserInfo(params, response['items']['token']);
       } else {
         EasyLoading.dismiss();
         ToastUtil.showWarningToast(response["msg"]);
       }
    });

  }

  /// 获取用户信息
  void getUserInfo(Map<String,String> map,String token){
    Options options = Options(
      headers: {
        "token": token
      }
    );
    ApiInstance().get("${StrUtil.userModule}/cgz/user/getUserInfo",queryParameters: map,options: options,errorFunction: (error){
      EasyLoading.dismiss();
      ToastUtil.showErrorToast("网络连接失败，请检查网络设置");
    }).then((resp){
      EasyLoading.dismiss();
      if(resp != null && resp['code'] == 200) {
        ToastUtil.showSuccessToast("登录成功");
        resp['data']['token'] = token;
        UserInfoEntity userInfoEntity = UserInfoEntity.fromJson(resp['data']);
        AppApplication.getInstance().userInfoEntity = userInfoEntity;
        SpUtil.instance.setString(StrUtil.userInfo,jsonEncode(userInfoEntity.toJson()));
        AppApplication.getCurrentState()?.pushNamedAndRemoveUntil(RoutePaths.mainIndex, (_)=>false);
      } else {
        String errorMsg = resp != null ? (resp['message'] ?? resp['msg'] ?? '登录失败，请稍后再试') : '登录失败，请稍后再试';
        ToastUtil.showErrorToast(errorMsg);
      }
    });
  }

  /// 微信登录
  void launchWithWeChat() async{
    if (state.privacyChecked) {
      Fluwx fluwx = Fluwx();
      if (await fluwx.isWeChatInstalled) {
        fluwx.addSubscriber((response) {
          if (response is WeChatAuthResponse) {
            oneClickLogin(wxCode: response.code??'');
          }
        });
        fluwx.authBy(which:  PhoneLogin(scope: 'snsapi_userinfo',state: 'qtzhWeChat')).then((value){
          print("是否授权成功------$value");
        });
      } else {
        ToastUtil.showToast("请安装微信客户端");
      }
    } else {
      ToastUtil.showNormalToast("请先同意隐私协议");
    }
  }

  /// 苹果登录
  void appleLogin() async {
    if (state.privacyChecked){
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        webAuthenticationOptions: WebAuthenticationOptions(
          // TODO: Set the `clientId` and `redirectUri` arguments to the values you entered in the Apple Developer portal during the setup
          clientId:
          'com.aboutyou.dart_packages.sign_in_with_apple.example',
          redirectUri: Uri.parse(
            'https://flutter-sign-in-with-apple-example.glitch.me/callbacks/sign_in_with_apple',
          ),
        ),
        // TODO: Remove these if you have no need for them
        nonce: 'example-nonce',
        state: 'example-state',
      );

      print("credential------$credential");
      print(
          "打印苹果鉴权的结果：---code:${credential.authorizationCode},\n---email:${credential.email},\n----familyName:${credential.familyName},\n-----userIdentifier:${credential.userIdentifier},\n----givenName:${credential.givenName},\n------identityToken:${credential.identityToken},\n-----state:${credential.state},\n");
      loginType = 2;
      oneClickLogin(identityToken: credential.identityToken!);

    } else {
      ToastUtil.showNormalToast("请先同意隐私协议");
    }
  }

  /// 一键登录
  void oneClickLogin({String identityToken = '',String mobile = '',String wxCode = ''}){
    Map<String,String> data = {};
    if(identityToken.isNotEmpty){
      data['identityToken'] = identityToken;
    }else if (mobile.isNotEmpty){
      data['mobile'] = mobile;
    }else if (wxCode.isNotEmpty){
      data['wxCode'] = wxCode;
    }
    ApiInstance().post("${StrUtil.userModule}/sys/login/oneClickLogin",data: data,errorFunction: (error){
      ToastUtil.showToast("网络出错了，请稍后再试");
    }).then((value){
      if(value['code']==200 && value['data'] != null){
        if(value['data']['loginInfo'] != null){
          SpUtil.instance.remove(StrUtil.userInfo);
          getUserInfo({'roleId': value["data"]["loginInfo"]['roleList'][0]['roleId'], 'device': 'app'}, value["data"]['loginInfo']['token']);
        }else {
          if(loginType == 0){
            mobile = value['data']['mobile'];
          }else if(loginType == 1){
            unionId = value['data']['unionId'];
          }else if(loginType == 2){
            appleId = value['data']['appleId'];
          }
          AppApplication.getCurrentState()?.pushNamed(RoutePaths.register,arguments: {
            'unionId': unionId,
            'appleId':appleId,
            'mobile':mobile,
            'loginType':loginType
          });
        }
      } else {
        ToastUtil.showToast(value['message']??value['msg']??value['data']);
      }
    });

  }

  //密码加密
  String generateMd5(String data) {
    var content = Utf8Encoder().convert(data);
    var digest = md5.convert(content);
    return digest.toString();
  }
}