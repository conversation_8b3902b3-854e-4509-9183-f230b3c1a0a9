import 'package:bloc_test/home/<USER>';
import 'package:bloc_test/profile/profile_view.dart';
import 'package:bloc_test/search/search_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'tab_bar_bloc.dart';
import 'tab_bar_event.dart';
import 'tab_bar_state.dart';

class TabBarPage extends StatelessWidget {
  const TabBarPage({super.key, });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => TabBarBloc()..add(TabBarChangeEvent(0)),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<TabBarBloc>(context);
    return BlocBuilder<TabBarBloc, TabBarState>(
      builder: (context, state) {
        return Scaffold(
          body: PageView(
            physics: const NeverScrollableScrollPhysics(),
            controller: bloc.pageController,
            onPageChanged: (index) {
              bloc.add(TabBarChangeEvent(index));
            },
            children: [
              AdvancedSliverHomePage(),
              SearchPage(),
              ProfilePage()
            ],
          ),
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: state.currentIndex,
            onTap: (index) {
              bloc.add(TabBarChangeEvent(index));
            },
            items: const [
              BottomNavigationBarItem(icon: Icon(Icons.home), label: '首页'),
              BottomNavigationBarItem(
                icon: Icon(Icons.search),
                label: '订单',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: '个人',
              ),
            ],
          ),
        );
      },
    );
  }
}
