import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';

import '../models/userInfo.dart';
import '../utils/AppApplication.dart';
import '../utils/StrUtil.dart';
import '../utils/sp_util.dart';
import 'tab_bar_event.dart';
import 'tab_bar_state.dart';

class TabBarBloc extends Bloc<TabBarEvent, TabBarState> {
  final PageController pageController = PageController(initialPage: 0);
  TabBarBloc() : super(TabBarState().init()) {
    on<TabBarChangeEvent>(handler);
  }
  void handler(TabBarChangeEvent event, Emitter<TabBarState> emit) async {
    state.currentIndex = event.index;
    emit(state.clone());
    pageController.jumpToPage(state.currentIndex);
    initUserInfo();
  }

  // 初始化用户信息
  void initUserInfo() {
    final userInfo = SpUtil.instance.getString(StrUtil.userInfo);
    if (userInfo.isEmpty) {
      AppApplication.getInstance().userInfoEntity = UserInfoEntity();
    } else {
      try {
        Map<String,dynamic> userMap = jsonDecode(userInfo) as Map<String, dynamic>;
        logger.d("获取的用户信息：$userInfo");
        AppApplication.getInstance().userInfoEntity = UserInfoEntity.fromJson(userMap);
      } catch (e) {
        AppApplication.getInstance().userInfoEntity = UserInfoEntity();
      }
    }
  }

}
