import 'package:bloc_test/models/userInfo.dart';

class TabBarState {
  late int currentIndex;
  late UserInfoEntity  userInfo;
  late bool isColorFiltered;
  TabBarState init() {
    return TabBarState()
    ..currentIndex = 0
      ..isColorFiltered = false
    ..userInfo = UserInfoEntity();
  }

  TabBarState clone() {
    return TabBarState()
    ..userInfo = userInfo
      ..isColorFiltered = isColorFiltered
      ..currentIndex  = currentIndex;
  }
}
