import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'protocol_bloc.dart';
import 'protocol_event.dart';
import 'protocol_state.dart';

class ProtocolPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => ProtocolBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<ProtocolBloc>(context);

    return Container();
  }
}

