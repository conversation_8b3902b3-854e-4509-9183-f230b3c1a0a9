import 'package:bloc_test/models/IndustryNewsModel.dart';
import 'package:bloc_test/utils/HttpState.dart';

class HomeState  {

  List menuData = [
    {
      'image': 'images/self_service_certificate.png',
      'title': '自助办证',
    },
    {
      'image': 'images/video_certification.png',
      'title': '视频办证',
    },
    {
      'image': 'images/multi_party_notarization.png',
      'title': '多方公证',
    },
    {
      'image': 'images/appointment_certificate.png',
      'title': '预约办证',
    },
    {
      'image': 'images/notary_meeting.png',
      'title': '公证会议',
    },
    {
      'image': 'images/judicial_expertise.png',
      'title': '司法鉴定',
    },
    {
      'image': 'images/notary_meeting.png',
      'title': '动画演示',
    },
    // {
    //   'image': 'lib/assets/images/qukuailian.png',
    //   'title': '金融赋强',
    // },
  ];
  List<IndustryNewsModel> industryList = [];
  int currentPage = 1;
  int pageSize = 10;
  int index = 0;
  late HttpState httpState;

  HomeState init() {
    return HomeState()
    ..httpState  = HttpState.init
      ..index = 0
    ..industryList = [];
  }

  HomeState clone() {
    return HomeState()
    ..currentPage = currentPage
      ..pageSize = pageSize
      ..httpState = httpState
      ..index = index
    ..industryList = industryList;
  }
}
