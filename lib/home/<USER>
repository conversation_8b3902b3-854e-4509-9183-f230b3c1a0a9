import 'package:bloc_test/utils/AppApplication.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../utils/appTheme.dart';
import '../utils/router.dart';
import '../utils/toast_util.dart';
import 'home_bloc.dart';
import 'home_event.dart';
import 'home_state.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key, });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => HomeBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<HomeBloc>(context);
    return BlocBuilder<HomeBloc, HomeState>(
      buildWhen: (previous, current) => previous != current,
      builder: (context, state) {
        return ColorFiltered(
          colorFilter: ColorFilter.mode(
            AppApplication.isColorFiltered ? Colors.grey : Colors.transparent,
            BlendMode.color,
          ),
          child: MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: SmartRefresher(
                controller: bloc.refreshController,
              enablePullDown: true,
              enablePullUp: true,
              header: WaterDropHeader(),
              footer: ClassicFooter(),
              onRefresh: (){
                  context.read<HomeBloc>().add(RefreshEvent());
              },
              onLoading: (){
                  context.read<HomeBloc>().add(LoadMoreEvent());
              },
              child: SingleChildScrollView(
                child: Stack(
                  children: [
                    Image.asset(
                      "images/home_back_new.png",
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      fit: BoxFit.fill,
                    ),
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        topTitle,
                        bonerContainer(context,state,bloc),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // 顶部文字显示
  Widget get topTitle => Row(
    children: [
      Padding(
        padding:
        EdgeInsets.only(top: 55.h, left: 25.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '青桐智盒',
              style: TextStyle(fontSize: 28),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              '智慧公证MINI一体机',
              style: TextStyle(fontSize: 17, color: Color(0xFFB8C1CE)),
            ),
          ],
        ),
      ),
      const Spacer()
    ],
  );

  // 下方圆角显示
  Widget  bonerContainer(BuildContext context,HomeState state,HomeBloc bloc) => Container(
    padding: EdgeInsets.symmetric(horizontal: 15.w),
    margin: EdgeInsets.only(top: 30.h),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15), topRight: Radius.circular(15)),
      boxShadow: [
        BoxShadow(
            color: AppTheme.darkGrey.withAlpha(10),
            blurRadius: 8,
            offset: Offset(0, -10))
      ],
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _gridWidget(context,state,bloc),
        _guideWidget(context,state,bloc),
        guideAndIntroduceWidget(context,state,bloc),
        _tabTitle(),
        _professionWidget(context,state,bloc),
      ],
    ),
  );

  // 菜单视图
  Widget _gridWidget(BuildContext context,HomeState state,HomeBloc bloc) => ColoredBox(
    color: Colors.transparent,
    child: MediaQuery.removePadding(
      context: context,
      removeBottom: true,
      removeTop: true,
      child: Padding(
        padding: EdgeInsets.only(top: 20.w),
        child: GridView.builder(
            itemCount: state.menuData.length,
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 1,
            ),
            itemBuilder: (context, index) {
              return InkWell(
                onTap: () async {
                  bloc.add(MenuEvent(index));
                },
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(
                      state.menuData[index]['image'],
                      width: 50,
                      height: 50,
                    ),
                    const SizedBox(
                      height: 6,
                    ),
                    Text(
                      state.menuData[index]['title'],
                      style: TextStyle(
                          color: AppTheme.textBlack_3,
                          fontSize: 14.sp),
                    ),
                  ],
                ),
              );
            }),
      ),
    ),
  );

  /// 申办帮助或业务咨询：4008001820
  Widget _guideWidget(BuildContext context,HomeState state,HomeBloc bloc) => InkWell(
    child: Container(
      margin: EdgeInsets.only(
          left: 10.w, right: 10.w, top: 20.w),
      height: 45.h,
      decoration: BoxDecoration(
          color: Color(0xFFF6F9FE),
          borderRadius: BorderRadius.circular(10)),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(
                left: 8.w, right: 15.w),
            child: Image.asset(
              "images/announcement.png",
              width: 20.w,
              fit: BoxFit.fill,
            ),
          ),
          Expanded(
              child: Text(
                "申办帮助或业务咨询：4008001820",
                style: TextStyle(
                    color: AppTheme.textBlack_3, fontSize: 15.sp),
              )),
        ],
      ),
    ),
    onTap: () {
      bloc.add(CallEvent());
    }
  );

  /// 办证帮助或公证介绍
  Widget  guideAndIntroduceWidget(BuildContext context,HomeState state,HomeBloc bloc) => Padding(
    padding: EdgeInsets.symmetric(
        horizontal: 10.w, vertical: 20.h),
    child: Row(
      children: [
        Expanded(
          flex: 1,
          child: InkWell(
            child: Container(
              height: 85.h,
              width: double.infinity,
              alignment: Alignment.bottomLeft,
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage("images/home_guide.png"),
                    fit: BoxFit.fill),
                color: Colors.transparent,
              ),
              child: Padding(
                padding: EdgeInsets.only(left: 10.w, bottom: 10.h),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "办证指引",
                      style: TextStyle(
                          fontSize: 15.sp, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height:3.h,
                    ),
                    Text(
                      "查看公证申办指引 >",
                      style:
                      TextStyle(fontSize: 12.sp, color: Color(0xFF5681D8), fontWeight: FontWeight.w500),
                    )
                  ],
                ),
              ),
            ),
            onTap: () {
              bloc.add(GetGuideEvent());
            },
          ),
        ),
        SizedBox(
          width: 11.w,
        ),
        Expanded(
          flex: 1,
          child: InkWell(
            child: Container(
              height: 85.h,
              alignment: Alignment.bottomLeft,
              decoration: BoxDecoration(
                image: DecorationImage(
                    image:
                    AssetImage("images/home_introduce.png"),
                    fit: BoxFit.fill),
                color: Colors.transparent,
              ),
              child: Padding(
                padding: EdgeInsets.only(left: 10.w, bottom: 10.h),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "公证介绍",
                      style: TextStyle(
                          fontSize: 15.sp, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: 3.h,
                    ),
                    Text(
                      "公证处及公证事项信息 >",
                      style:
                      TextStyle(fontSize: 12.sp, color: Color(0xFFAF9065),fontWeight: FontWeight.w500),
                    )
                  ],
                ),
              ),
            ),
            onTap: () {
              bloc.add(GetNotaryEvent());
            },
          ),
        )
      ],
    ),
  );

  Widget _tabTitle() => Container(
    color: Colors.white,
    padding: EdgeInsets.only(left: 10.w, bottom: 10.h),
    child: Align(
      alignment: Alignment.centerLeft,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 10.h,
          ),
          Text(
            "行业动态",
            style: TextStyle(
              fontSize: 17.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    ),
  );

  /// 行业动态
  Widget _professionWidget(BuildContext context,HomeState state,HomeBloc bloc) => MediaQuery.removePadding(
      removeTop: true,
      context: context,
      child: ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: state.industryList.length,
          itemBuilder: (ctx, i) {
            var item = state.industryList[i];
            return InkWell(
              onTap: () {
                // Consolidated navigation logic to prevent multiple simultaneous navigation calls
                bloc.judgeLogin(judgeResult: () {
                  // Only navigate if user is logged in and verified
                  AppApplication.getCurrentState()?.pushNamed(RoutePaths.news, arguments: item);
                }, loginCallBack: () {
                  AppApplication.getCurrentState()?.pushNamed(
                      RoutePaths.login);
                }, alertResult: () {
                  return ToastUtil.showWarningToast("请先实名认证！");
                });
              },
              child: Container(
                margin:  EdgeInsets.fromLTRB(0, 0, 0, 10.w),
                padding:  EdgeInsets.all(15.w),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(8.w)),
                    boxShadow: [
                      BoxShadow(
                          color: Colors.grey.withAlpha(10),
                          offset: Offset(0, 10))
                    ]),
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child:CachedNetworkImage(
                          imageUrl: item.picture??'',
                          width: 100,
                          height: 100,
                          fit: BoxFit.fill,
                        memCacheHeight: 100,
                        memCacheWidth: 100,
                        placeholder: (ctx,url){
                          return SizedBox(
                            width: 100,
                            height: 100,
                            child: ColoredBox(
                              color: AppTheme.bgB,
                            ),
                          );
                        },
                        errorWidget: (ctx,obj,tra){
                            return SizedBox(
                              width: 100,
                              height: 100,
                              child: ColoredBox(
                                color: AppTheme.bgB,
                              ),
                            );
                        },
                      )
                    ),
                    const SizedBox(
                      width: 20,
                    ),
                    Expanded(
                      child: SizedBox(
                        height: 100.h,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(item.title ?? '',
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w700,
                                      fontSize: 16,
                                      color: Color(0xff4B4B4B))),
                            ),
                            SizedBox(height: 8.h),
                            Text(
                              item.notarialName ?? '',
                              style: TextStyle(color: Color(0xff888888)),
                            ),
                            Text(
                              item.createDate ?? '',
                              style: TextStyle(color: Color(0xff888888)),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            );
          }));
}
