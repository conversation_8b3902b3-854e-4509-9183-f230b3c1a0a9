
import 'dart:math';

import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/IndustryNewsModel.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:url_launcher/url_launcher.dart';

import '../utils/AppApplication.dart';
import '../utils/common_tools.dart';
import '../utils/sp_util.dart';
import '../utils/toast_util.dart';
import 'home_event.dart';
import 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  RefreshController refreshController = RefreshController(initialRefresh: false);
  HomeBloc() : super(HomeState().init()) {
    on<InitEvent>(_init);
    on<RefreshEvent>(_refresh);
    on<LoadMoreEvent>(_loadMore);
    on<GetGuideEvent>(_guideEvent);
    on<GetNotaryEvent>(_notaryEvent);
    on<CallEvent>(_callEvent);
    on<MenuEvent>(_menuEvent);
    on<ListItemTapEvent>(_listItemTapEvent);
  }
  void _menuEvent(MenuEvent event,Emitter<HomeState> emit) async {
    // 使用防抖动避免重复点击
    _handleMenuAction(event.index);
  }

  void _listItemTapEvent(ListItemTapEvent event,Emitter<HomeState> emit) async {
    // 使用防抖动避免重复点击
   judgeLogin(judgeResult: (){
     AppApplication.getCurrentState()?.pushNamed(RoutePaths.news, arguments: event.model);
   }, alertResult: (){
     return ToastUtil.showWarningToast("请先实名认证");
   }, loginCallBack: (){
     AppApplication.getCurrentState()?.pushNamed(RoutePaths.login);
   });
  }

  void _handleMenuAction(int index) {
    if (index != 5) {
      judgeLogin(judgeResult: () {
        switch (index) {
          case 0:
            if(AppApplication.getInstance().userInfoEntity.idCard != null && AppApplication.getInstance().userInfoEntity.idCard?.length == 18){
              String userBirthDay = CommonTools.getBirthDayFromCardId(AppApplication.getInstance().userInfoEntity.idCard??'');
              int userAge = CommonTools.getAgeFromBirthday(userBirthDay);
              if (userAge >= 18){
                AppApplication.getCurrentState()?.pushNamed(RoutePaths.explain, arguments: 1);
              } else {
                ToastUtil.showToast('当前检测到你的年龄未满18周岁暂无法办理当前业务');
              }
            } else {
              AppApplication.getCurrentState()?.pushNamed(RoutePaths.explain, arguments: 1);
            }
            break;
          case 1:
            AppApplication.getCurrentState()?.pushNamed(RoutePaths.explain, arguments: 2);
            break;
          case 2:
            AppApplication.getCurrentState()?.pushNamed(RoutePaths.explain, arguments: 3);
            break;
          case 3:
            AppApplication.getCurrentState()?.pushNamed(RoutePaths.appointment);
            break;
          case 4:
            AppApplication.getCurrentState()?.pushNamed(RoutePaths.videoConference);
            break;
          case 6:
            // 动画演示页面，不需要登录验证
            AppApplication.getCurrentState()?.pushNamed(RoutePaths.animationDemo, arguments: {});
            break;
        // case 7:
        //   G.pushNamed(RoutePaths.financialNotarization);
        //   break;
        }
      }, loginCallBack: () {
        AppApplication.getCurrentState()?.pushNamed(RoutePaths.login);
      }, alertResult: () {
        return ToastUtil.showWarningToast("请先实名认证");
      });
    } else {
      bool isOne = SpUtil.instance.getBool("isOne");
      if(!isOne){
        AppApplication.getCurrentState()?.pushNamed(RoutePaths.judicialExpertise);
      }else{
        // requestPrivacy();
      }
    }
  }
  void _callEvent(CallEvent event,Emitter<HomeState> emit) async {
    canLaunchUrl(Uri(scheme: "tel", path: "4008001820"));
  }

  void _guideEvent(GetGuideEvent event,Emitter<HomeState> emit) async {
    AppApplication.getCurrentState()?.pushNamed(RoutePaths.guide);
  }

  void _notaryEvent(GetNotaryEvent event,Emitter<HomeState> emit) async {
    judgeLogin(judgeResult: (){
      AppApplication.getCurrentState()?.pushNamed(RoutePaths.notaryOffice);
    }, alertResult: (){
      AppApplication.getCurrentState()?.pushNamed(RoutePaths.notaryOffice);
    }, loginCallBack: (){
      AppApplication.getCurrentState()?.pushNamed(RoutePaths.login);
    });

  }

  void _init(InitEvent event, Emitter<HomeState> emit) async {
    // Initialize user info from AppApplication
    state.currentPage = 1;
    Map<String, dynamic> map = {
      "currentPage": state.currentPage,
      "pageSize": state.pageSize,
      //
    };
   final response = await ApiInstance().post("${StrUtil.parkModule}/sq/question/industryNews",data: map,errorFunction: (error){
      logger.d("error---------$error");
    });
     if (response['code'] == 200) {
      List<dynamic> list = response['items'];
      List<IndustryNewsModel> temp = [];
      for (var e in list) {
        temp.add(IndustryNewsModel.fromJson(e));
      }
      state.industryList.clear();
      state.industryList.addAll(temp);
      state.currentPage = 1;
      emit(state.clone());
    }
  }

  void _refresh(RefreshEvent event,Emitter<HomeState> emit)async {
    state.currentPage = 1;
    Map<String, dynamic> map = {
      "currentPage": state.currentPage,
      "pageSize": state.pageSize,
      //
    };
   final response = await  ApiInstance().post("${StrUtil.parkModule}/sq/question/industryNews",data: map,errorFunction: (error){
      logger.d("error---------$error");
    });
      if (response['code'] == 200) {
        List<dynamic> list = response['items'];
        List<IndustryNewsModel> temp = [];
        for (var e in list) {
          temp.add(IndustryNewsModel.fromJson(e));
        }
        state.industryList.clear();
        state.industryList.addAll(temp);
        state.currentPage = 1;
        refreshController.refreshCompleted();
        emit(state.clone());
      }
  }

  void _loadMore(LoadMoreEvent event,Emitter<HomeState> emit) async{
    state.currentPage++;
    Map<String, dynamic> map = {
      "currentPage": state.currentPage,
      "pageSize": state.pageSize,
      //
    };
   final response = await ApiInstance().post("${StrUtil.parkModule}/sq/question/industryNews",data: map,errorFunction: (error){
      logger.d("error---------$error");
    });
    if (response['code'] == 200) {
      List<dynamic> list = response['items'];
      if (list.isNotEmpty) {
        List<IndustryNewsModel> temp = [];
        for (var e in list) {
          temp.add(IndustryNewsModel.fromJson(e));
        }
        state.industryList.addAll(temp);
        refreshController.loadComplete();
        emit(state.clone());
      } else {
        refreshController.loadNoData();
      }
    }
  }

  void judgeLogin(
      {required Function judgeResult, Function? loginCallBack,required Function alertResult}) {
    /// 未登录
    if (!AppApplication.getInstance().hasUser) {
      if (loginCallBack != null) {
        loginCallBack.call();
      }
      /// 登录
    } else {
      judgeIdentify(judgeResult: judgeResult, alertResult: alertResult);
    }
  }

  /// 是否实名
  void judgeIdentify({Function? alertResult,required Function judgeResult}) {
    if (AppApplication.getInstance().userInfoEntity.idCard == null || AppApplication.getInstance().userInfoEntity.idCard == "") {
      //未实名
      if (alertResult != null) {
        alertResult.call();
      }
    } else {
      // 实名
      judgeResult.call();
    }
  }
}
