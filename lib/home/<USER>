import 'package:bloc_test/models/IndustryNewsModel.dart';

abstract class HomeEvent {}

class InitEvent extends HomeEvent {}

class RefreshEvent extends HomeEvent {}
class LoadMoreEvent extends HomeEvent {}

// 办证指引
class GetGuideEvent extends HomeEvent {}

//公证介绍
class GetNotaryEvent extends HomeEvent {}

// 拨打电话
class CallEvent extends HomeEvent {}

// 菜单栏点击事件
class MenuEvent extends HomeEvent {
  final int index;
  MenuEvent(this.index);
}

class ListItemTapEvent extends HomeEvent {
  final IndustryNewsModel model;
  ListItemTapEvent(this.model);
}