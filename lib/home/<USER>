import 'package:bloc_test/models/IndustryNewsModel.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../utils/appTheme.dart';
import '../utils/router.dart';
import '../utils/toast_util.dart';
import 'home_bloc.dart';
import 'home_event.dart';
import 'home_state.dart';

/// 高级 Sliver 布局的主页面
class AdvancedSliverHomePage extends StatefulWidget {
  const AdvancedSliverHomePage({super.key});

  @override
  State<AdvancedSliverHomePage> createState() => _AdvancedSliverHomePageState();
}

class _AdvancedSliverHomePageState extends State<AdvancedSliverHomePage>
    with TickerProviderStateMixin,AutomaticKeepAliveClientMixin {
  late ScrollController _scrollController;
  late AnimationController _fabAnimationController;
  bool _showFab = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final shouldShow = _scrollController.offset > 200;
    if (shouldShow != _showFab) {
      setState(() {
        _showFab = shouldShow;
      });
      if (_showFab) {
        _fabAnimationController.forward();
      } else {
        _fabAnimationController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return BlocProvider(
      create: (BuildContext context) => HomeBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<HomeBloc>(context);
    return ColorFiltered(
      colorFilter: ColorFilter.mode(
        AppApplication.isColorFiltered ? Colors.grey : Colors.transparent,
        BlendMode.color,
      ),
      child: Scaffold(
        body: SmartRefresher(
          controller: bloc.refreshController,
          enablePullUp: true,
          enablePullDown: true,
          header: const WaterDropMaterialHeader(),
          footer: const ClassicFooter(),
          onLoading: () async {
            bloc.add(LoadMoreEvent());
          },
          onRefresh: () async {
            context.read<HomeBloc>().add(RefreshEvent());
          },
          child: CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // 可折叠的顶部区域
              _buildSliverAppBar(context),
              // 菜单网格
              _buildSliverMenuGrid(context),
              // 帮助区域
              _buildSliverHelpSection(context, bloc),
              // 粘性标题
              _buildSliverPersistentHeader(),
              // 新闻列表
              _buildSliverNewsList(),
            ],
          ),
        ),
        // 回到顶部按钮
        floatingActionButton: ScaleTransition(
          scale: _fabAnimationController,
          child: FloatingActionButton(
            mini: true,
            onPressed: () {
              _scrollController.animateTo(
                0,
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeInOut,
              );
            },
            backgroundColor: const Color(0xFF5681D8),
            child: const Icon(Icons.keyboard_arrow_up, color: Colors.white),
          ),
        ),
      ),
    );
  }

  /// 构建可折叠的 SliverAppBar
  Widget _buildSliverAppBar(BuildContext context) {
    return SliverAppBar(
      expandedHeight: 180.h,
      floating: false,
      pinned: true,
      stretch: true,
      backgroundColor: AppTheme.themeBlue,
      elevation: 0,
      leading: Container(), // 隐藏默认的返回按钮
      flexibleSpace: FlexibleSpaceBar(
        title: AnimatedOpacity(
          opacity: _showFab ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Text(
            '青桐智盒',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        background: Stack(
          fit: StackFit.expand,
          children: [
            // 背景图片
            Image.asset("images/img_1.png", fit: BoxFit.cover),
            // 标题内容
            Positioned(
              left: 25.w,
              bottom: 40.h,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Hero(
                    tag: 'app_title',
                    child: Material(
                      color: Colors.transparent,
                      child: Text(
                        '青桐智盒',
                        style: TextStyle(
                          fontSize: 32.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          shadows: [
                            Shadow(
                              offset: const Offset(2, 2),
                              blurRadius: 4,
                              color: Colors.black.withOpacity(0.5),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '智慧公证MINI一体机',
                    style: TextStyle(
                      fontSize: 18.sp,
                      color: Colors.white.withOpacity(0.9),
                      shadows: [
                        Shadow(
                          offset: const Offset(1, 1),
                          blurRadius: 2,
                          color: Colors.black.withOpacity(0.3),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建菜单网格 Sliver
  Widget _buildSliverMenuGrid(BuildContext context) {
    return SliverPadding(
      padding: EdgeInsets.all(20.w),
      sliver: BlocBuilder<HomeBloc, HomeState>(
        builder: (context, state) {
          final block = context.read<HomeBloc>();
          return SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisSpacing: 15,
              crossAxisSpacing: 15,
              childAspectRatio: 0.85,
            ),
            delegate: SliverChildBuilderDelegate((context, index) {
              final menuItem = state.menuData[index];
              return _buildAnimatedMenuItem(
                context,
                menuItem['image'],
                menuItem['title'],
                index,
                () => block.add(MenuEvent(index)),
              );
            }, childCount: state.menuData.length),
          );
        },
      ),
    );
  }

  /// 构建带动画的菜单项
  Widget _buildAnimatedMenuItem(
    BuildContext context,
    String image,
    String title,
    int index,
    VoidCallback onTap,
  ) {
    return TweenAnimationBuilder<double>(
      key: ValueKey(title),
      duration: Duration(milliseconds: 300 + (index * 100)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Opacity(
            opacity: value,
            child: GestureDetector(
              onTap: onTap,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.15),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Hero(
                      tag: 'menu_icon_$index',
                      child: Image.asset(
                        image,
                        width: 36.w,
                        height: 36.h,
                        fit: BoxFit.contain,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      title,
                      style: TextStyle(
                        color: AppTheme.textBlack_3,
                        fontSize: 11.sp,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建帮助区域 Sliver
  Widget _buildSliverHelpSection(BuildContext context, HomeBloc bloc) {
    return SliverToBoxAdapter(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          children: [
            // 申办帮助
            _buildHelpCard(context, bloc),
            SizedBox(height: 15.h),
            // 办证指引和公证介绍
            Row(
              children: [
                Expanded(child: _buildGuideCard(context, bloc)),
                SizedBox(width: 15.w),
                Expanded(child: _buildIntroduceCard(context, bloc)),
              ],
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }

  /// 申办帮助卡片
  Widget _buildHelpCard(BuildContext context, HomeBloc bloc) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => bloc.add(CallEvent()),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 55.h,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [const Color(0xFFF6F9FE), const Color(0xFFE8F2FF)],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFE8F2FF), width: 1),
          ),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.w),
                child: Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: const Color(0xFF5681D8).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.phone_in_talk,
                    color: const Color(0xFF5681D8),
                    size: 20.w,
                  ),
                ),
              ),
              Expanded(
                child: Text(
                  "申办帮助或业务咨询：4008001820",
                  style: TextStyle(
                    color: AppTheme.textBlack_3,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: const Color(0xFF5681D8),
                  size: 16.w,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 办证指引卡片
  Widget _buildGuideCard(BuildContext context, HomeBloc bloc) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => bloc.add(GetGuideEvent()),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 120.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF6B9EFF), Color(0xFF5681D8)],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF5681D8).withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.description_outlined,
                    color: Colors.white,
                    size: 20.w,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "办证指引",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      "查看公证申办指引",
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 公证介绍卡片
  Widget _buildIntroduceCard(BuildContext context, HomeBloc bloc) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => bloc.add(GetNotaryEvent()),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 120.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFFFFB366), Color(0xFFAF9065)],
            ),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFAF9065).withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(15.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.info_outline,
                    color: Colors.white,
                    size: 20.w,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "公证介绍",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      "公证处及公证事项信息",
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建粘性标题
  Widget _buildSliverPersistentHeader() {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _StickyHeaderDelegate(
        minHeight: 60.h,
        maxHeight: 60.h,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                width: 4.w,
                height: 24.h,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFF6B9EFF), Color(0xFF5681D8)],
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                "行业动态",
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textBlack_3,
                ),
              ),
              const Spacer(),
              // Text(
              //   "更多",
              //   style: TextStyle(
              //     fontSize: 14.sp,
              //     color: const Color(0xFF5681D8),
              //     fontWeight: FontWeight.w500,
              //   ),
              // ),
              // SizedBox(width: 5.w),
              // Icon(
              //   Icons.arrow_forward_ios,
              //   color: const Color(0xFF5681D8),
              //   size: 14.w,
              // ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建新闻列表 Sliver
  Widget _buildSliverNewsList() {
   return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        if (state.industryList.isEmpty) {
          return SliverToBoxAdapter(
            child: SizedBox(
              height: 300.h,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.article_outlined,
                      size: 64.w,
                      color: Colors.grey[300],
                    ),
                    SizedBox(height: 20.h),
                    Text(
                      "暂无行业动态",
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.grey[500],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      "下拉刷新获取最新内容",
                      style: TextStyle(fontSize: 14.sp, color: Colors.grey[400]),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
        return SliverPadding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate((context, index) {
              final item = state.industryList[index];
              return _buildAnimatedNewsItem(context, item, index);
            }, childCount: state.industryList.length),
          ),
        );
      },
    );

  }

  /// 构建带动画的新闻项
  Widget _buildAnimatedNewsItem(
    BuildContext context,
    IndustryNewsModel item,
    int index,
  ) {
    return TweenAnimationBuilder<double>(
      key: ObjectKey(item),
      duration: Duration(milliseconds: 200 + (index * 50)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        final bloc = context.read<HomeBloc>();
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Padding(
              padding: EdgeInsets.only(bottom: 15.h),
              child: GestureDetector(
                onTap: () => bloc.add(ListItemTapEvent(item)),
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.08),
                        spreadRadius: 1,
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 新闻图片
                      Hero(
                        tag: 'news_image_$index',
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: CachedNetworkImage(
                            imageUrl: item.picture ?? '',
                            width: 90.w,
                            height: 90.h,
                            fit: BoxFit.cover,
                            memCacheWidth: 90,
                            memCacheHeight: 90,
                            placeholder:
                                (context, url) => Container(
                                  width: 90.w,
                                  height: 90.h,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.image,
                                    color: Colors.grey[400],
                                    size: 32.w,
                                  ),
                                ),
                            errorWidget:
                                (context, url, error) => Container(
                                  width: 90.w,
                                  height: 90.h,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.broken_image,
                                    color: Colors.grey[400],
                                    size: 32.w,
                                  ),
                                ),
                          ),
                        ),
                      ),
                      SizedBox(width: 16.w),
                      // 新闻内容
                      Expanded(
                        child: SizedBox(
                          height: 90.h,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 标题
                              Expanded(
                                flex: 3,
                                child: Text(
                                  item.title ?? '',
                                  style: TextStyle(
                                    fontSize: 15.sp,
                                    fontWeight: FontWeight.w600,
                                    color: const Color(0xFF333333),
                                    height: 1.4,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              const Spacer(),
                              // 公证处名称
                              Text(
                                item.notarialName ?? '',
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  color: const Color(0xFF666666),
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: 4.h),
                              // 创建时间
                              Row(
                                children: [
                                  Icon(
                                    Icons.access_time,
                                    size: 12.w,
                                    color: Colors.grey[400],
                                  ),
                                  SizedBox(width: 4.w),
                                  Text(
                                    item.createDate ?? '',
                                    style: TextStyle(
                                      fontSize: 11.sp,
                                      color: const Color(0xFF999999),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}

/// 粘性标题代理
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;

  _StickyHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_StickyHeaderDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
