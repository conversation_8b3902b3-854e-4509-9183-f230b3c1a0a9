import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'guide_bloc.dart';
import 'guide_event.dart';

class GuidePage extends StatelessWidget {
  const GuidePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => GuideBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final block = BlocProvider.of<GuideBloc>(context);
    return Scaffold(
      appBar: AppBar(title: Text("办证指引"),),
      body: SafeArea(
        child: Column(
          children: [
            Container(
              height: 175.h,
              width: double.infinity,
              padding: EdgeInsets.only(
                  left: 45.w,
                  top: 10.h,
                  right: 45.w,
                  bottom: 20.h),
              decoration: BoxDecoration(
                  image:  DecorationImage(
                      image:  AssetImage("images/bzzn.png"),
                      fit: BoxFit.fill)),
              child: Center(
                  child: Text("    清楚自己办理的哪种公证的可以选择“自助公证”，不清楚的可以选择视频公证。",
                      style: TextStyle(color: Colors.white, fontSize: 16.sp),
                      textAlign: TextAlign.left)),
            ),

            _cardItem(title: "自助公证", content: "自助公证是在明确自己所需公证的事项时，提供的快速办理通道。", imageBg: "images/自助公证_bg.png", image: "images/自助公证.png", index: 1, block: block),
            _cardItem(title: "视频公证", content: "视频公证是与公证员面对面的进行你所需业务的快捷公证通道。", imageBg: "images/视频公证_bg.png", image: "images/视频公证.png", index: 2, block: block),
            _cardItem(title: "合同公证", content: "合同公证是提供给合同双方的快捷视频公证通道。", imageBg: "images/合同公证_bg.png", image: "images/合同公证.png", index: 3, block: block),
          ],
        ),
      ),
    );
  }

  // card item

Widget _cardItem({required String title, required String content, required String imageBg,required String image,required int index,required GuideBloc block}) {
    return       GestureDetector(
      onTap: () {
        block.add(JumpEvent(index));
      },
      child: Container(
        height: 80.h,
        margin: EdgeInsets.only(
          top: 25.h,
          left: 25.w,
          right: 25.w,
        ),
        decoration: BoxDecoration(
            image: DecorationImage(
                image: AssetImage(imageBg),
                fit: BoxFit.fill)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            Container(
              height: 30.h,
              width: 40.w,
              margin: EdgeInsets.only(left: 20.w),
              decoration: BoxDecoration(
                  image: DecorationImage(
                      image:
                      AssetImage(image),
                      fit: BoxFit.contain)),
            ),
            Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: TextStyle(color: Colors.white, fontSize: 16.sp),
                      ),
                      SizedBox(
                        height: 4.h,
                      ),
                      Text(
                        content,
                        style: TextStyle(color: Colors.white, fontSize: 14.sp),
                      ),
                    ],
                  ),
                )),

          ],
        ),
      ),
    );
}
}

