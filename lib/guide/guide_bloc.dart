import 'package:bloc/bloc.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/router.dart';

import '../utils/toast_util.dart';
import 'guide_event.dart';
import 'guide_state.dart';

class GuideBloc extends Bloc<GuideEvent, GuideState> {
  GuideBloc() : super(GuideState().init()) {
    on<InitEvent>(_init);
    on<JumpEvent>(_jumpEvent);
  }

  void _jumpEvent(JumpEvent event, Emitter<GuideState> emit) async {
    emit(state.clone());
    if (event.index == 3) {
      ToastUtil.showWarningToast("暂无介绍");
    } else {
      AppApplication.getCurrentState()?.pushNamed(RoutePaths.guideDetailPage,arguments: event.index);
    }
  }
  void _init(InitEvent event, Emitter<GuideState> emit) async {
    emit(state.clone());
  }
}
