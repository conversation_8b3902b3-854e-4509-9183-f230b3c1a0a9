import 'package:bloc/bloc.dart';

import 'search_event.dart';
import 'search_state.dart';

class SearchBloc extends Bloc<SearchEvent, SearchState> {
  SearchBloc() : super(SearchState().init()) {
    on<InitEvent>(_init);
    on<SearchOrderDetailEvent>(_searchOrderDetail);
    on<SearchTabChangeEvent>(_searchTabChange);
  }

  void _init(InitEvent event, Emitter<SearchState> emit) async {
    emit(state.clone());
  }

  void _searchOrderDetail(SearchOrderDetailEvent event, Emitter<SearchState> emit) async {
    emit(state.clone());
  }

  void _searchTabChange(SearchTabChangeEvent event, Emitter<SearchState> emit) async {
    emit(state.clone()..selectedTab = event.index);
  }

}
