import 'package:flutter/material.dart';

class SearchState {
  // 假数据
  final List<String> tabs = ['视频办证', '自助办证', '多方公证', '对公公证'];
  int selectedTab = 3; // 默认选中“对公公证”
  final List<Map<String, dynamic>> orders = [
    {
      'type': '对公公证',
      'status': '完成',
      'statusColor': Colors.blue,
      'applyNo': '202309142111121119',
      'item': '对公公证',
      'time': '2023-09-14 21:13:47',
    },
    {
      'type': '对公公证',
      'status': '受理中',
      'statusColor': Colors.lightBlue,
      'applyNo': '202212091010181234',
      'item': '对公公证',
      'time': '2022-12-09 10:10:18',
    },
    {
      'type': '对公公证',
      'status': '待受理',
      'statusColor': Colors.orange,
      'applyNo': '202211151714491234',
      'item': '对公公证',
      'time': '2022-11-15 17:14:49',
    },
  ];

  String orderNo = '';
  SearchState init() {
    return SearchState()
    ..orderNo = '';
  }

  SearchState clone() {
    return SearchState()
    ..orderNo = orderNo;
  }
}
