import 'package:bloc_test/search/search_state.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'search_bloc.dart';
import 'search_event.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({super.key, });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => SearchBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    return BlocBuilder<SearchBloc, SearchState>(
  builder: (context, state) {
    final bloc = BlocProvider.of<SearchBloc>(context);
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        title: const Text('订单查询', style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold)),
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: Row(
        children: [
          // 左侧Tab
          Container(
            width: 100,
            color: Colors.white,
            child: ListView.builder(
              itemCount: state.tabs.length,
              itemBuilder: (context, index) {
                final bool selected = index == state.selectedTab;
                return GestureDetector(
                  onTap: () {
                    bloc.add(SearchTabChangeEvent(index));
                  },
                  child: Container(
                    color: selected ? const Color(0xFF3CA0FF) : Colors.white,
                    height: 60,
                    alignment: Alignment.center,
                    child: Text(
                      state.tabs[index],
                      style: TextStyle(
                        color: selected ? Colors.white : Colors.black87,
                        fontWeight: selected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 16,
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          // 右侧订单卡片
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
              itemCount: state.orders.length,
              itemBuilder: (context, index) {
                final order = state.orders[index];
                return Card(
                  color: AppTheme.white,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Image.asset(state.selectedTab == 0 ? "images/shiping.png" : (state.selectedTab == 1 ? "images/zzbz.png" : state.selectedTab == 2 ? "images/dfgz.png" :"images/dggz.png") , width: 20.w, height: 20.w),
                            const SizedBox(width: 8),
                            Text(state.tabs[state.selectedTab], style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                            const Spacer(),
                            Text(order['status'], style: TextStyle(color: order['statusColor'], fontSize: 15, fontWeight: FontWeight.bold)),
                          ],
                        ),
                        _rowItem("申请号", order["applyNo"]),
                        _rowItem("公证事项", order["item"]),
                        _rowItem("申请时间", order["time"]),
                        const SizedBox(height: 15),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            OutlinedButton(
                              onPressed: () {},
                              style: OutlinedButton.styleFrom(
                                padding: EdgeInsets.symmetric(horizontal: 40, vertical: 0),
                                side: BorderSide(color: Color(0xFF3CA0FF)),
                                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                              ),
                              child: const Text('详情', style: TextStyle(color: Color(0xFF3CA0FF))),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  },
);
  }

  Widget _rowItem(String title, String content) {
    return
    Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: TextStyle(color: Colors.grey)),
      const SizedBox(width: 8),
      Expanded(child: Text(content, style: const TextStyle(color: Colors.black),textAlign: TextAlign.end,)),
      ],
      ),
    );
  }

}

