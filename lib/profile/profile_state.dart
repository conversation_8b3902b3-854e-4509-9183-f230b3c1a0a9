import 'package:bloc_test/models/userInfo.dart';

class ProfileState {
  ProfileState init() {
    return ProfileState();
  }

  ProfileState clone() {
    return ProfileState();
  }
}

class UserProfileInitial extends ProfileState {}

class UserProfileLoading extends ProfileState {}

class UserProfileLoaded extends ProfileState {
  final UserInfoEntity profile;
  UserProfileLoaded(this.profile);
}

class AvatarPickInProgress extends ProfileState {}

class AvatarCropInProgress extends ProfileState {}

class AvatarUploadInProgress extends ProfileState {
  final double progress;
  AvatarUploadInProgress(this.progress);
}

class AvatarUploadSuccess extends ProfileState {
  final UserInfoEntity profile;
  AvatarUploadSuccess(this.profile);
}

class UserProfileError extends ProfileState {
  final String message;
  UserProfileError(this.message);
}
