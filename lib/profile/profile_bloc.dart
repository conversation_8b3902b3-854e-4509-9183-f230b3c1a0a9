import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/userInfo.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/common_tools.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc() : super(ProfileState().init()) {
    on<InitEvent>(_init);
    on<SettingEvent>(_settingEvent);
    on<LogOutEvent>(_logOutEvent);
    on<PickImageFromGallery>((event, emit) async {
  emit(AvatarPickInProgress());
  try {
    final image = await pickImageFromGallery();
    if (image != null) {
      add(CropImage(image));
    } else {
      emit(UserProfileError('未选择图片'));
    }
  } catch (e) {
    emit(UserProfileError('选择图片失败'));
  }
});

on<CropImage>((event, emit) async {
  emit(AvatarCropInProgress());
  try {
    final cropped = await cropImage(event.imageFile);
    if (cropped != null) {
      add(UploadAvatar(cropped));
    } else {
      emit(UserProfileError('裁剪失败'));
    }
  } catch (e) {
    emit(UserProfileError('裁剪异常'));
  }
});

on<UploadAvatar>((event, emit) async {
  emit(AvatarUploadInProgress(0));
  try {
    await for (final progress in uploadAvatar(event.avatarFile)) {
      emit(AvatarUploadInProgress(progress));
    }
    final updatedProfile = await fetchUserProfile();
    emit(AvatarUploadSuccess(updatedProfile));
  } catch (e) {
    emit(UserProfileError('上传失败'));
  }
});
  }

  void _init(InitEvent event, Emitter<ProfileState> emit) async {
    emit(state.clone());
  }

  void _logOutEvent(LogOutEvent event, Emitter<ProfileState> emit) async {
    emit(state.clone());
    SpUtil.instance.remove(StrUtil.userInfo);
    AppApplication().userInfoEntity = UserInfoEntity();
    AppApplication.getCurrentState()?.pushNamed(RoutePaths.login);
  }

  void _settingEvent(SettingEvent event, Emitter<ProfileState> emit) async {
    emit(state.clone());
    AppApplication.getCurrentState()?.pushNamed(RoutePaths.settings);
  }

  /// 选择图片
  Future<File?> pickImageFromGallery() async {
    XFile? pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery);
    return pickedFile != null ? File(pickedFile.path) : null;
  }

  /// 裁剪图片
  Future<File?> cropImage(File imageFile) async {
    final croppedFile = await ImageCropper().cropImage(
        sourcePath: imageFile.path,
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 100,
        // iosUiSettings: [
        //   AndroidUiSettings(
        //     toolbarTitle: 'Cropper',
        //     toolbarColor: Colors.deepOrange,
        //     toolbarWidgetColor: Colors.white,
        //     initAspectRatio: CropAspectRatioPreset.square,
        //     lockAspectRatio: false,
        //   ),
        //   IOSUiSettings(
        //     title: 'Cropper',
        //   ),
        // ],
      );
      return croppedFile!= null? File(croppedFile.path) : null;
  }

  /// 上传头像
  Stream<double> uploadAvatar(File avatarFile) async* {
    // 模拟上传进度
    for (int i = 0; i <= 100; i++) {
      await Future.delayed(Duration(milliseconds: 100));
      yield i / 100.0; // 进度为0.0 ~ 1.0
    } 
  }

  /// 获取用户信息
  Future<UserInfoEntity> fetchUserProfile() async {
    // 模拟获取用户信息
    await Future.delayed(Duration(seconds: 1)); // 模拟网络请求
    return UserInfoEntity(userName: 'John Doe', headIcon: "");  
  }

}
