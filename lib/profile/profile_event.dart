import 'dart:io';

abstract class ProfileEvent {}

class InitEvent extends ProfileEvent {}

class LoadUserProfile extends ProfileEvent {}

class AvatarTapped extends ProfileEvent {}

class PickImageFromCamera extends ProfileEvent {}

class PickImageFromGallery extends ProfileEvent {}

class ImagePicked extends ProfileEvent {
  final File imageFile;
  ImagePicked(this.imageFile);
}

class CropImage extends ProfileEvent {
  final File imageFile;
  CropImage(this.imageFile);
}

class ImageCropped extends ProfileEvent {
  final File croppedFile;
  ImageCropped(this.croppedFile);
}

class UploadAvatar extends ProfileEvent {
  final File avatarFile;
  UploadAvatar(this.avatarFile);
}

class UploadProgressChanged extends ProfileEvent {
  final double progress; // 0.0 ~ 1.0
  UploadProgressChanged(this.progress);
}

class UploadFailed extends ProfileEvent {
  final String error;
  UploadFailed(this.error);
}

class RetryUpload extends ProfileEvent {}


class SettingEvent extends ProfileEvent{}

class LogOutEvent extends ProfileEvent {}

class HelpAndFeedBackEvent extends ProfileEvent{}

class AboutUsEvent extends ProfileEvent{}