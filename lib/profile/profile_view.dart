import 'package:bloc_test/utils/appTheme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'profile_bloc.dart';
import 'profile_event.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key, });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => ProfileBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<ProfileBloc>(context);
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: Column(
        children: [
          // 顶部蓝色渐变背景和个人信息
          Container(
            width: double.infinity,
            height: 240,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF3CA0FF), Color(0xFF5AC8FA)],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 30),
                const Text(
                  '个人中心',
                  style: TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                CircleAvatar(
                  radius: 44,
                  backgroundColor: Colors.white,
                  child: CircleAvatar(
                    radius: 40,
                    backgroundImage: AssetImage('images/userImage.png'), // 替换为你的头像图片
                  ),
                ),
                const SizedBox(height: 12),
                GestureDetector(
                  onTap: () {
                    // 跳转登录
                  },
                  child: const Text(
                    '立即登录',
                    style: TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // 菜单卡片
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Card(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              elevation: 2,
              color: AppTheme.white,
              child: Column(
                children: [
                  _buildMenuItem(
                    icon: Icons.assignment_outlined,
                    iconColor: Color(0xFF4DD599),
                    title: '我的订单',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: Icons.qr_code,
                    iconColor: Color(0xFF3CA0FF),
                    title: '二维码扫描',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: Icons.verified_user_outlined,
                    iconColor: Color(0xFFFFA940),
                    title: '实名认证',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: Icons.location_on_outlined,
                    iconColor: Color(0xFF3CA0FF),
                    title: '我的地址',
                    onTap: () {},
                  ),
                  _buildMenuItem(
                    icon: Icons.stars_outlined,
                    iconColor: Color(0xFFFF5B5B),
                    title: '我的签章',
                    onTap: () {},
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Card(
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
              elevation: 2,
              color: AppTheme.white,
              child: Column(
                children: [
                  _buildMenuItem(
                    icon: Icons.settings_outlined,
                    iconColor: Color(0xFFFFA940),
                    title: '系统设置',
                    onTap: () {
                      bloc.add(SettingEvent());
                    },
                  ),
                  _buildMenuItem(
                    icon: Icons.phone_in_talk_outlined,
                    iconColor: Color(0xFFFFC542),
                    title: '咨询热线',
                    trailing: const Text('4008001820', style: TextStyle(color: Colors.black87)),
                    onTap: () {},
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    Widget? trailing,
    required VoidCallback onTap,
  }) {
    return ListTile(

      leading: Icon(icon, color: iconColor, size: 28),
      title: Text(title, style: const TextStyle(fontSize: 16)),
      trailing: trailing ?? const Icon(Icons.chevron_right, color: Colors.grey),
      onTap: onTap,
    );
  }
}
