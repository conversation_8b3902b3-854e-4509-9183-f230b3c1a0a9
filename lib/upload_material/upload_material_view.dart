import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'upload_material_bloc.dart';
import 'upload_material_event.dart';
import 'upload_material_state.dart';

class UploadMaterialPage extends StatelessWidget {
  const UploadMaterialPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => UploadMaterialBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final block = context.read<UploadMaterialBloc>();
    return Container();
  }
}

