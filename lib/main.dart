import 'dart:async';

import 'package:bloc_test/tab_bar/tab_bar_view.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/MyRouteObserver.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/common_tools.dart';
import 'package:bloc_test/utils/custom_animation.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:umeng_apm_sdk/umeng_apm_sdk.dart';

class MyApmWidgetsFlutterBinding extends ApmWidgetsFlutterBinding {
  static WidgetsBinding ensureInitialized() {
    MyApmWidgetsFlutterBinding();
    return WidgetsBinding.instance;
  }
}
void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.dark
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.yellow
    ..backgroundColor = Colors.green
    ..indicatorColor = Colors.yellow
    ..textColor = Colors.yellow
    ..maskColor = Colors.blue.withOpacity(0.5)
    ..userInteractions = true
    ..dismissOnTap = false
    ..customAnimation = CustomAnimation();
}

void main() {
  runZonedGuarded(
    () {
      final UmengApmSdk umengApmSdk = UmengApmSdk(
        name: "",
        bver: "",
        enableLog: true,
        flutterVersion: "3.29.3",
        engineVersion: "2.10.0",
        enableTrackingPageFps: true,
        enableTrackingPagePerf: true,
        initFlutterBinding: MyApmWidgetsFlutterBinding.ensureInitialized,
        onError: (error, stack) {
          logger.d("error------$error,stack------------$stack");
        },
      );
      umengApmSdk.init(
        appRunner: (observer) async {
          PackageInfo packageInfo = await PackageInfo.fromPlatform();
          String packageName = packageInfo.packageName;
          String buildNumber = packageInfo.buildNumber;
          String version = packageInfo.version;
          umengApmSdk.name = packageName;
          umengApmSdk.bver = '$version+$buildNumber';
          await SpUtil.instance.init();
          CommonTools.getProjectEncryptSetting();
          ///横竖屏
          SystemChrome.setPreferredOrientations([
            DeviceOrientation.portraitUp,
            DeviceOrientation.portraitDown,
          ]);
          configLoading();
          return MyApp(navigatorObserver: observer);
        },
      );
    },
    (Object obj, StackTrace details) {
      logger.e(details.toString());
    },
  );
}

class MyApp extends StatelessWidget {
  final NavigatorObserver navigatorObserver;

  const MyApp({super.key, required this.navigatorObserver});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: ScreenUtilInit(
        designSize: const Size(375, 812),
        child: RefreshConfiguration(
          hideFooterWhenNotFull: false,
          child: MaterialApp(
            navigatorKey: AppApplication.navigatorKey,
            navigatorObservers: [navigatorObserver, MyRouteObserver()],
            title: '青桐智盒',
            locale: const Locale('zh', 'CN'),
            builder: EasyLoading.init(),
            localizationsDelegates: [
              RefreshLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            onGenerateRoute: RouterPage.getRoutes,
            theme: ThemeData(
              colorScheme: ColorScheme.fromSeed(seedColor: AppTheme.themeBlue,primary: AppTheme.themeBlue),
              appBarTheme: AppBarTheme(
                backgroundColor: AppTheme.themeBlue,
                iconTheme: IconThemeData(color: Colors.white,size: 24),
                centerTitle: true,
                titleTextStyle: TextStyle(
                  color: AppTheme.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w700
                )
              ),
              textSelectionTheme: TextSelectionThemeData(
                cursorColor: AppTheme.themeBlue, // 全局设置光标颜色为蓝色
              ),
            ),
            home: const TabBarPage(),
          ),
        ),
      ),
    );
  }
}
