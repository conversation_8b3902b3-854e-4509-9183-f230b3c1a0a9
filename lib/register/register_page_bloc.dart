import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/block_puzzle_captcha.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../utils/common_tools.dart';
import '../utils/event_bus_instance.dart';
import 'register_page_event.dart';
import 'register_page_state.dart';

class RegisterPageBloc extends Bloc<RegisterPageEvent, RegisterPageState> {
  Timer? countTimer;

  int count = 120;

  RegisterPageBloc() : super(RegisterPageState().init()) {
    on<InitEvent>(_init);
    on<RegisterPhoneChanged>(_phoneChanged);
    on<RegisterEmailChanged>(_registerEmailChanged);
    on<RegisterCodeChanged>(_codeChanged);
    on<RegisterPasswordChanged>(_passwordChanged);
    on<RegisterConfirmPasswordChanged>(_confirmPasswordChanged);
    on<RegisterPasswordVisibilityToggled>(_passwordVisibilityToggled);
    on<RegisterConfirmPasswordVisibilityToggled>(
      _confirmPasswordVisibilityToggled,
    );
    on<RegisterPrivacyCheckedChanged>(_privacyCheckedChanged);
    on<RegisterGetCodePressed>(_getCodePressed);
    on<RegisterSubmitted>(_registerSubmitted);
    on<RegisterTabChangeEvent>(_tabIndexChanged);
    on<RegisterCountdownUpdateEvent>(_countdownUpdate);
    on<ResetCountDownStateEvent>(_resetCountDownStateEvent);
    on<ResetRegisterSubmitStateEvent>(_resetRegisterSubmitStateEvent);
    on<StartTimerStateEvent>(_startTimerStateEvent);
  }

  void _init(InitEvent event, Emitter<RegisterPageState> emit) {
    emit(state.clone(appleId: event.appleId, unionId: event.unionId));
  }

  void _phoneChanged(
    RegisterPhoneChanged event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(phone: event.phone));
  }

  void _registerEmailChanged(
    RegisterEmailChanged event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(email: event.email));
  }

  void _codeChanged(
    RegisterCodeChanged event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(code: event.code));
  }

  void _passwordChanged(
    RegisterPasswordChanged event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(password: event.password));
  }

  void _confirmPasswordChanged(
    RegisterConfirmPasswordChanged event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(confirmPassword: event.confirmPassword));
  }

  void _passwordVisibilityToggled(
    RegisterPasswordVisibilityToggled event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(showPassword: !state.showPassword));
  }

  void _confirmPasswordVisibilityToggled(
    RegisterConfirmPasswordVisibilityToggled event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(showConfirmPassword: !state.showConfirmPassword));
  }

  void _privacyCheckedChanged(
    RegisterPrivacyCheckedChanged event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(privacyChecked: event.checked));
    SpUtil.instance.setBool(StrUtil.isAgree, event.checked);

  }

  void _getCodePressed(
    RegisterGetCodePressed event,
    Emitter<RegisterPageState> emit,
  ) async {
    if (state.index == 0) {
      if (state.phone.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }
      if (state.phone.length != 11) {
        ToastUtil.showWarningToast("请输入正确的手机号");
        return;
      }
    } else if (state.index == 1) {
      if (state.phone.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }
      if (state.email.isEmpty) {
        ToastUtil.showWarningToast("请输入邮箱");
        return;
      }

      if (!RegexUtil.isEmail(state.email)) {
        ToastUtil.showWarningToast("邮箱格式错误");
        return;
      }
    }
    emit(state.clone(canGetCode: true,isSendingCode:  true));
    getImageCode();
  }

  void _registerSubmitted(
    RegisterSubmitted event,
    Emitter<RegisterPageState> emit,
  ) {
    if (state.index == 0) {
      if (state.phone.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }
      if (state.phone.length != 11) {
        ToastUtil.showWarningToast("请输入正确的手机号");
        return;
      }
      if (state.code.isEmpty) {
        ToastUtil.showWarningToast("请输入验证码");
        return;
      }
      if (state.code.length != 6) {
        ToastUtil.showWarningToast("请输入正确的验证码");
        return;
      }
      if (state.password.length < 8 || state.password.length > 16) {
        ToastUtil.showWarningToast("请输入8-16位密码");
        return;
      }
      if (!CommonTools.checkPassWord(state.password)) {
        ToastUtil.showWarningToast("密码必须包含大写字母、小写字母、数字、特殊字符三种及三种以上");
        return;
      }
      if (state.password != state.confirmPassword) {
        ToastUtil.showWarningToast("两次输入的密码不一致");
        return;
      }
      if (!state.privacyChecked) {
        ToastUtil.showWarningToast("请阅读并同意《青桐智盒隐私政策》和《服务协议》");
        return;
      }

    } else {
      if (state.phone.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号！");
        return;
      }
      if (state.email.isEmpty) {
        ToastUtil.showWarningToast("请输入邮箱！");
        return;
      }

      if (!RegexUtil.isEmail(state.email)) {
        ToastUtil.showWarningToast("邮箱格式错误！");
        return;
      }
      if (state.code.isEmpty) {
        ToastUtil.showWarningToast("请输入验证码！");
        return;
      }
      if (state.password.length < 8 || state.password.length > 16) {
        ToastUtil.showWarningToast("密码长度为8-16位！");
        return;
      }

      if (state.password != state.confirmPassword) {
        ToastUtil.showWarningToast("两次密码不一致！");
        return;
      }

      if (!CommonTools.checkPassWord(state.password)) {
        ToastUtil.showWarningToast("密码必须包含大写字母、小写字母、数字、特殊字符三种及三种以上");
        return;
      }

      if (!state.privacyChecked) {
        ToastUtil.showWarningToast("请阅读并同意《青桐智盒隐私政策》和《服务协议》");
        return;
      }
    }
    emit(state.clone(isLoading: false));
    registerUser();
  }

  void _tabIndexChanged(
    RegisterTabChangeEvent event,
    Emitter<RegisterPageState> emit,
  ) {
    countTimer?.cancel();
    count = 120;
    emit(
      state.clone(canGetCode: true, countString: "获取验证码", index: event.index),
    );
  }

  void _countdownUpdate(
    RegisterCountdownUpdateEvent event,
    Emitter<RegisterPageState> emit,
  ) {
    if (event.count == 0) {
      emit(state.clone(canGetCode: true, countString: "获取验证码",isSendingCode: false));
    } else {
      emit(state.clone(countString: '${event.count} S'));
    }
  }

  void _resetCountDownStateEvent(
    ResetCountDownStateEvent event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(canGetCode: true, countString: "获取验证码",isSendingCode: false));
  }
  void _resetRegisterSubmitStateEvent(
    ResetRegisterSubmitStateEvent event,
    Emitter<RegisterPageState> emit,
  ) {
    emit(state.clone(isLoading: false));
  }

  void _startTimerStateEvent(
    StartTimerStateEvent event,
    Emitter<RegisterPageState> emit,
  )async{
    countTimer?.cancel();
    count = 120;
    count--;
    emit(state.clone(canGetCode: false,countString: "$count S",isSendingCode: false));
    countTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      count--;
      if (count == 0) {
        countTimer?.cancel();
      }
      add(RegisterCountdownUpdateEvent(count));
    });
  }

  @override
  Future<void> close() {
    countTimer?.cancel();
    return super.close();
  }

  // 获取图像验证码接口
  void getImageCode() {
    showDialog(
      context: AppApplication.currentContext!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BlockPuzzleCaptchaPage(
          onSuccess: (v, token) {
            if (state.index == 1) {
              getAbroadEmailCode(v, token);
            } else {
              getChinaCountrySmsCode(v, token);
            }
          },
          onFail: () {},
          close: () {
            if (state.isSendingCode) {
              add(ResetCountDownStateEvent(false, true));
            }
          },
        );
      },
    );
  }

  void getAbroadEmailCode(String pointJson, String token) {
    if (state.phone.isEmpty) {
      ToastUtil.showWarningToast("请输入手机号");
      return;
    }
    if (state.email.isEmpty) {
      ToastUtil.showWarningToast("请输入邮箱");
      return;
    }
    if (!RegexUtil.isEmail(state.email)) {
      ToastUtil.showWarningToast("请输入正确的邮箱");
      return;
    }
    if (pointJson.isEmpty) {
      ToastUtil.showWarningToast("请先验证图形验证码");
      return;
    }
    Map<String, dynamic> map = {
      "email": state.email,
      "pointJson": pointJson,
      "token": token,
    };

    ApiInstance()
        .post(
          "${StrUtil.userModule}/cgz/userApp/sendEmailCode",
          data: map,
          errorFunction: (error) {
            ToastUtil.showErrorToast(StrUtil.httpError);
            add(ResetCountDownStateEvent(false, true));
          },
        )
        .then((data) {
          if (data != null && data["code"] == 200) {
            ToastUtil.showSuccessToast("验证码获取成功！");
            AppApplication.getCurrentState()?.pop();
            add(StartTimerStateEvent());
          } else if (data != null && data['code'] == 12003) {
            eventBus.fire(EventBusInstanceEvent(source: 'checkFail'));
            add(ResetCountDownStateEvent(false, true));
          } else {
            ToastUtil.showWarningToast(data['data'] ?? data['message']);
            add(ResetCountDownStateEvent(false, true));
          }
        });
  }

  void getChinaCountrySmsCode(String pointJson, String token) {
    if (state.phone.isEmpty) {
      ToastUtil.showWarningToast('请输入手机号码');
      return;
    }
    if (state.phone.length != 11) {
      ToastUtil.showWarningToast('请输入正确的手机号码');
      return;
    }
    if (pointJson.isEmpty) {
      ToastUtil.showWarningToast('请先验证图形验证码');
      return;
    }
    Map<String, String> params = {
      "areaCode": "86",
      "mobile": state.phone,
      "sendType": "1",
      "type": "2",
      "pointJson": pointJson,
      "token": token,
    };
    ApiInstance()
        .get(
          "${StrUtil.userModule}/sys/Sms/domesticSend",
          queryParameters: params,
          errorFunction: (error) {
            ToastUtil.showErrorToast(StrUtil.httpError);
            add(ResetCountDownStateEvent(false, true));
          },
        )
        .then((data) {
          if (data != null && data["code"] == 200) {
            ToastUtil.showSuccessToast("验证码获取成功！");
            AppApplication.getCurrentState()?.pop();
            add(StartTimerStateEvent());
          } else if (data != null && data['code'] == 12003) {
            eventBus.fire(EventBusInstanceEvent(source: 'checkFail'));
            add(ResetCountDownStateEvent(false, true));
          } else {
            ToastUtil.showWarningToast(data['data'] ?? data["message"]);
            add(ResetCountDownStateEvent(false, true));
          }
        });
  }

  void registerUser() {
    Map<String, String> map = {};
    if (state.index == 0) {
      map = {
        "password": CommonTools.generateMd5(state.password),
        "confirmPassword": CommonTools.generateMd5(state.password),
        "mobile": state.phone,
        'appleId': state.appleId,
        "unionId": state.unionId,
        "smsCode": state.code,
        "areaCode": '86',
      };
    } else {
      map = {
        'mobile': state.phone,
        'password': CommonTools.generateMd5(state.password),
        'confirmPassword':CommonTools.generateMd5(state.confirmPassword),
        'appleId': state.appleId,
        "unionId": state.unionId,
        'email': state.email,
        'emailCode': state.code
      };
    }
    EasyLoading.show();
    ApiInstance()
        .post(
          "${StrUtil.userModule}/sys/login/oneClickRegist",
          data: map,
          errorFunction: (error) {
            EasyLoading.dismiss();
            ToastUtil.showErrorToast(StrUtil.httpError);
            add(ResetRegisterSubmitStateEvent(false));
          },
        )
        .then((res) {
          EasyLoading.dismiss();
          if (res != null) {
            logger.d("参数1：$res");
            if (res["code"] != 200) {
              ToastUtil.showWarningToast(res["data"] ?? res['message']);
              add(ResetRegisterSubmitStateEvent(false));
              return;
            }
            AppApplication.getCurrentState()?.popAndPushNamed(RoutePaths.login);
          } else {
            ToastUtil.showWarningToast("注册失败，稍后再试！");
            add(ResetRegisterSubmitStateEvent(false));
          }
        });
  }
}
