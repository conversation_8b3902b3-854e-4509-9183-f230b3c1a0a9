abstract class RegisterPageEvent {}

/// 手机号
class RegisterPhoneChanged extends RegisterPageEvent {
  final String phone;
  RegisterPhoneChanged(this.phone);
}

/// 验证码
class RegisterCodeChanged extends RegisterPageEvent {
  final String code;
  RegisterCodeChanged(this.code);
}

/// 密码
class RegisterPasswordChanged extends RegisterPageEvent {
  final String password;
  RegisterPasswordChanged(this.password);
}

/// 确认密码
class RegisterConfirmPasswordChanged extends RegisterPageEvent {
  final String confirmPassword;
  RegisterConfirmPasswordChanged(this.confirmPassword);
}

/// 邮箱
class RegisterEmailChanged extends RegisterPageEvent {
  final String email;
  RegisterEmailChanged(this.email);
}

/// 密码可见性
class RegisterPasswordVisibilityToggled extends RegisterPageEvent {}

/// 确认密码可见性
class RegisterConfirmPasswordVisibilityToggled extends RegisterPageEvent {}


class RegisterPrivacyCheckedChanged extends RegisterPageEvent {
  final bool checked;
  RegisterPrivacyCheckedChanged(this.checked);
}

/// 获取验证码
class RegisterGetCodePressed extends RegisterPageEvent {}

/// 注册
class RegisterSubmitted extends RegisterPageEvent {}

/// 初始化
class InitEvent extends RegisterPageEvent {
  final String appleId;
  final String unionId;
  InitEvent(this.appleId, this.unionId);
}

/// 切换注册方式
class RegisterTabChangeEvent extends RegisterPageEvent {
  final int index;
  RegisterTabChangeEvent(this.index);
}

/// 倒计时更新
class RegisterCountdownUpdateEvent extends RegisterPageEvent {
  final int count;
  RegisterCountdownUpdateEvent(this.count);
}

/// 重置倒计时状态
class ResetCountDownStateEvent extends RegisterPageEvent {
  final bool isSendingCode;
  final bool canGetCode;
  ResetCountDownStateEvent(this.isSendingCode, this.canGetCode);
}

/// 重置注册按钮状态
class ResetRegisterSubmitStateEvent extends RegisterPageEvent {
  final bool canRegister;
  ResetRegisterSubmitStateEvent(this.canRegister);
}

class StartTimerStateEvent extends RegisterPageEvent {}
