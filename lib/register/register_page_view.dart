import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'register_page_bloc.dart';
import 'register_page_event.dart';
import 'register_page_state.dart';

class RegisterPage extends StatelessWidget {
  final String unionId;
  final String appleId;
  final String mobile;
  final int loginType;
  const RegisterPage({
    super.key,
    required this.unionId,
    required this.appleId,
    required this.mobile,
    required this.loginType,
  });
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (BuildContext context) =>
              RegisterPageBloc()..add(InitEvent(appleId, unionId)),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppTheme.themeBlue,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.arrow_back_ios, color: Colors.white),
        ),
        title: Text(
          '注册账号',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        centerTitle: true,
        actions: [
          TextButton(
            onPressed: () {
              // 跳转注册说明
            },
            child: Text(
              '注册说明',
              style: TextStyle(color: Colors.white, fontSize: 14.sp),
            ),
          ),
        ],
      ),
      body: BlocConsumer<RegisterPageBloc, RegisterPageState>(
        listener: (context, state) {
          // 可以在这里添加错误提示等监听逻辑
        },
        builder: (context, state) {
          final bloc = context.read<RegisterPageBloc>();

          return SingleChildScrollView(
            child: Column(
              children: [
                // 顶部插画
                Container(
                  height: 280.h,
                  width: double.infinity,
                  child: Image.asset('images/logoimg.png', fit: BoxFit.contain),
                ),

                // 表单内容
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Column(
                    children: [
                      // Tab切换
                      _buildTabBar(context, state, bloc),

                      SizedBox(height: 30.h),

                      // 表单字段
                      _buildFormFields(context, state, bloc),

                      SizedBox(height: 40.h),

                      // 注册按钮
                      _buildRegisterButton(context, state, bloc),

                      SizedBox(height: 20.h),

                      // 协议
                      _buildAgreement(context, state, bloc),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabBar(
    BuildContext context,
    RegisterPageState state,
    RegisterPageBloc bloc,
  ) {
    return Container(
      height: 50.h,
      decoration: BoxDecoration(
        color: AppTheme.bgE,
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => bloc.add(RegisterTabChangeEvent(0)),
              child: Container(
                height: 50.h,
                decoration: BoxDecoration(
                  color: state.index == 0 ? Colors.white : Colors.transparent,
                  borderRadius: BorderRadius.circular(25.r),
                  boxShadow:
                      state.index == 0
                          ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ]
                          : null,
                ),
                child: Center(
                  child: Text(
                    '手机号注册',
                    style: TextStyle(
                      color:
                          state.index == 0
                              ? AppTheme.themeBlue
                              : AppTheme.textBlack_1,
                      fontSize: 16.sp,
                      fontWeight:
                          state.index == 0 ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => bloc.add(RegisterTabChangeEvent(1)),
              child: Container(
                height: 50.h,
                decoration: BoxDecoration(
                  color: state.index == 1 ? Colors.white : Colors.transparent,
                  borderRadius: BorderRadius.circular(25.r),
                  boxShadow:
                      state.index == 1
                          ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ]
                          : null,
                ),
                child: Center(
                  child: Text(
                    '邮箱注册',
                    style: TextStyle(
                      color:
                          state.index == 1
                              ? AppTheme.themeBlue
                              : AppTheme.textBlack_1,
                      fontSize: 16.sp,
                      fontWeight:
                          state.index == 1 ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields(
    BuildContext context,
    RegisterPageState state,
    RegisterPageBloc bloc,
  ) {
    return Column(
      children: [
        // 手机号输入框
        _buildInputField(
          label: '手机号码',
          hint: '请输入手机号',
          keyboardType: TextInputType.phone,
          onChanged: (value) => bloc.add(RegisterPhoneChanged(value)),
        ),

        SizedBox(height: 20.h),

        // 邮箱输入框（仅在邮箱注册时显示）
        if (state.index == 1) ...[
          _buildInputField(
            label: '邮箱地址',
            hint: '请输入邮箱账号',
            keyboardType: TextInputType.emailAddress,
            onChanged: (value) => bloc.add(RegisterEmailChanged(value)),
          ),
          SizedBox(height: 20.h),
        ],

        // 验证码输入框
        _buildVerificationCodeField(context, state, bloc),

        SizedBox(height: 20.h),

        // 密码输入框
        _buildPasswordField(
          label: '设置密码',
          hint: '输入8位以上有效密码',
          isVisible: state.showPassword,
          onChanged: (value) => bloc.add(RegisterPasswordChanged(value)),
          onToggleVisibility:
              () => bloc.add(RegisterPasswordVisibilityToggled()),
        ),

        SizedBox(height: 20.h),

        // 确认密码输入框
        _buildPasswordField(
          label: '确认密码',
          hint: '请再次输入密码',
          isVisible: state.showConfirmPassword,
          onChanged: (value) => bloc.add(RegisterConfirmPasswordChanged(value)),
          onToggleVisibility:
              () => bloc.add(RegisterConfirmPasswordVisibilityToggled()),
        ),

        SizedBox(height: 15.h),

        // 密码规则提示
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
          decoration: BoxDecoration(
            color: AppTheme.bgE,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            '密码必须包含大写字母、小写字母、数字、特殊字符其中三种',
            style: TextStyle(color: AppTheme.textBlack_1, fontSize: 12.sp),
          ),
        ),
      ],
    );
  }

  Widget _buildInputField({
    required String label,
    required String hint,
    required Function(String) onChanged,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            keyboardType: keyboardType,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: Colors.grey.shade400, fontSize: 14),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              onChanged(value);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationCodeField(
    BuildContext context,
    RegisterPageState state,
    RegisterPageBloc bloc,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '验证码',
              style: TextStyle(
                color: AppTheme.textBlack,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  keyboardType: TextInputType.number,
                  onChanged: (value) => bloc.add(RegisterCodeChanged(value)),
                  decoration: InputDecoration(
                    hintText: '请输入短信验证码',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 14.sp,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 15.w),
            SizedBox(
              width: 120.w,
              height: 40.h,
              child: ElevatedButton(
                onPressed:
                    state.canGetCode && !state.isSendingCode
                        ? () => bloc.add(RegisterGetCodePressed())
                        : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      state.canGetCode
                          ? AppTheme.themeBlue
                          : Colors.grey.shade300,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child:
                    state.isSendingCode
                        ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : Text(
                          state.countString,
                          style: TextStyle(
                            color:
                                state.canGetCode
                                    ? Colors.white
                                    : AppTheme.textBlack_1,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPasswordField({
    required String label,
    required String hint,
    required bool isVisible,
    required Function(String) onChanged,
    required VoidCallback onToggleVisibility,
  }) {

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            keyboardType: TextInputType.visiblePassword,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: Colors.grey.shade400, fontSize: 14),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              suffixIcon: IconButton(
                onPressed: onToggleVisibility,
                icon: Icon(
                  isVisible ? Icons.visibility : Icons.visibility_off,
                  color: AppTheme.textBlack_1,
                  size: 20.sp,
                ),
              ),
            ),
            onChanged: (value) {
              onChanged(value);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterButton(
    BuildContext context,
    RegisterPageState state,
    RegisterPageBloc bloc,
  ) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton(
        onPressed:
            state.canRegister ? () => bloc.add(RegisterSubmitted()) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.themeBlue,
          disabledBackgroundColor: AppTheme.bgB,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25.r),
          ),
          elevation: 0,
        ),
        child: Text(
          '立即注册',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildAgreement(
    BuildContext context,
    RegisterPageState state,
    RegisterPageBloc bloc,
  ) {
    return Row(
      children: [
        Checkbox(
          value: state.privacyChecked,
          onChanged: (value) => bloc.add(RegisterPrivacyCheckedChanged(value!)),
          activeColor: AppTheme.themeBlue,
        ),
        Expanded(
          child: RichText(
            text: TextSpan(
              text: "已阅读和同意",
              style: TextStyle(fontSize: 12.sp, color: AppTheme.textBlack),
              children: [
                TextSpan(
                  text: "《隐私政策》",
                  style: TextStyle(color: AppTheme.themeBlue),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          AppApplication.getCurrentState()?.pushNamed(
                            RoutePaths.webViewWidget,
                            arguments: {
                              "title": "隐私政策",
                              "url": StrUtil.privacyPolicy,
                            },
                          );
                        },
                ),
                TextSpan(
                  text: "和",
                  style: TextStyle(color: AppTheme.textBlack),
                ),
                TextSpan(
                  text: "《服务协议》",
                  style: TextStyle(color: AppTheme.themeBlue),
                  recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          // 跳转服务协议
                        },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
