class RegisterPageState {
  final String phone;
  final String code;
  final String email;
  final String password;
  final String confirmPassword;
  final bool showPassword;
  final bool showConfirmPassword;
  final bool privacyChecked;
  final bool isLoading;
  final int index;
  final String countString;
  final bool canGetCode;
  final bool isSendingCode;
  final String appleId;
  final String unionId;

  RegisterPageState({
    this.phone = '',
    this.email = '',
    this.code = '',
    this.password = '',
    this.confirmPassword = '',
    this.appleId = '',
    this.unionId = '',
    this.index = 0,
    this.countString = '获取验证码',
    this.canGetCode = true,
    this.showPassword = false,
    this.showConfirmPassword = false,
    this.privacyChecked = false,
    this.isSendingCode = false,
    this.isLoading = false,
  });

  RegisterPageState init() {
    return RegisterPageState(
      phone: '',
      email: '',
      code: '',
      password: '',
      confirmPassword: '',
      appleId: '',
      unionId: '',
      index: 0,
      canGetCode: true,
      countString: '获取验证码',
      showPassword: false,
      showConfirmPassword: false,
      privacyChecked: false,
      isSendingCode: false,
    );
  }
  RegisterPageState clone({
    String? phone,
    String? code,
    String? email,
    String? password,
    String? confirmPassword,
    bool? showPassword,
    bool? showConfirmPassword,
    bool? privacyChecked,
    int? index,
    String? countString,
    bool? canGetCode,
    String? appleId,
    String? unionId,
    bool? isSendingCode,
    bool? isLoading,
  }) {
    return RegisterPageState(
      phone: phone ?? this.phone,
      email: email ?? this.email,
      code: code ?? this.code,
      password: password ?? this.password,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      appleId: appleId ?? this.appleId,
      unionId: unionId ?? this.unionId,
      index: index ?? this.index,
      canGetCode: canGetCode ?? this.canGetCode,
      countString: countString ?? this.countString,
      showPassword: showPassword ?? this.showPassword,
      showConfirmPassword: showConfirmPassword ?? this.showConfirmPassword,
      privacyChecked: privacyChecked ?? this.privacyChecked,
      isSendingCode: isSendingCode ?? this.isSendingCode,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get canRegister {
    if (index == 0) {
      return phone.isNotEmpty && code.isNotEmpty && password.isNotEmpty && confirmPassword.isNotEmpty && privacyChecked;
    } else {
      return phone.isNotEmpty && code.isNotEmpty && email.isNotEmpty && password.isNotEmpty && confirmPassword.isNotEmpty && privacyChecked ;
    }
  }
}