import 'package:flutter/material.dart';
import 'package:bloc_test/utils/navigation_helper.dart';
import 'package:bloc_test/utils/page_transitions.dart';

/// 转场动画演示页面
class AnimationDemoPage extends StatelessWidget {
  const AnimationDemoPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('转场动画演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.count(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          children: [
            _buildAnimationButton(
              context,
              '淡入淡出',
              Icons.opacity,
              Colors.blue,
              () => NavigationHelper.fadeNavigateTo(
                const DemoTargetPage(title: '淡入淡出动画'),
              ),
            ),
            _buildAnimationButton(
              context,
              '左滑进入',
              Icons.arrow_forward,
              Colors.green,
              () => NavigationHelper.slideNavigateTo(
                const DemoTargetPage(title: '左滑进入动画'),
                slideType: PageTransitionType.slideLeft,
              ),
            ),
            _buildAnimationButton(
              context,
              '右滑进入',
              Icons.arrow_back,
              Colors.orange,
              () => NavigationHelper.slideNavigateTo(
                const DemoTargetPage(title: '右滑进入动画'),
                slideType: PageTransitionType.slideRight,
              ),
            ),
            _buildAnimationButton(
              context,
              '上滑进入',
              Icons.arrow_upward,
              Colors.purple,
              () => NavigationHelper.slideNavigateTo(
                const DemoTargetPage(title: '上滑进入动画'),
                slideType: PageTransitionType.slideUp,
              ),
            ),
            _buildAnimationButton(
              context,
              '下滑进入',
              Icons.arrow_downward,
              Colors.red,
              () => NavigationHelper.slideNavigateTo(
                const DemoTargetPage(title: '下滑进入动画'),
                slideType: PageTransitionType.slideDown,
              ),
            ),
            _buildAnimationButton(
              context,
              '缩放动画',
              Icons.zoom_in,
              Colors.teal,
              () => NavigationHelper.scaleNavigateTo(
                const DemoTargetPage(title: '缩放动画'),
              ),
            ),
            _buildAnimationButton(
              context,
              '旋转动画',
              Icons.rotate_right,
              Colors.indigo,
              () => NavigationHelper.rotateNavigateTo(
                const DemoTargetPage(title: '旋转动画'),
              ),
            ),
            _buildAnimationButton(
              context,
              'iOS风格',
              Icons.phone_iphone,
              Colors.grey,
              () => NavigationHelper.cupertinoNavigateTo(
                const DemoTargetPage(title: 'iOS风格动画'),
              ),
            ),
            _buildAnimationButton(
              context,
              'Material风格',
              Icons.android,
              Colors.lightGreen,
              () => NavigationHelper.materialNavigateTo(
                const DemoTargetPage(title: 'Material风格动画'),
              ),
            ),
            _buildAnimationButton(
              context,
              '滑动+旋转',
              Icons.rotate_90_degrees_ccw,
              Colors.deepOrange,
              () => NavigationHelper.customNavigateTo(
                const DemoTargetPage(title: '滑动+旋转动画'),
                transitionType: PageTransitionType.slideRotate,
              ),
            ),
            _buildAnimationButton(
              context,
              '缩放+旋转',
              Icons.crop_rotate,
              Colors.pink,
              () => NavigationHelper.customNavigateTo(
                const DemoTargetPage(title: '缩放+旋转动画'),
                transitionType: PageTransitionType.scaleRotate,
              ),
            ),
            _buildAnimationButton(
              context,
              '共享轴动画',
              Icons.swap_horiz,
              Colors.cyan,
              () => NavigationHelper.sharedAxisNavigateTo(
                const DemoTargetPage(title: '共享轴动画'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimationButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withOpacity(0.8),
                color.withOpacity(0.6),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 40,
                color: Colors.white,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 演示目标页面
class DemoTargetPage extends StatelessWidget {
  final String title;
  
  const DemoTargetPage({
    Key? key,
    required this.title,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 100,
              color: Colors.green,
            ),
            const SizedBox(height: 20),
            Text(
              title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            const Text(
              '这是一个演示页面\n展示不同的转场动画效果',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 12,
                ),
              ),
              child: const Text('返回'),
            ),
          ],
        ),
      ),
    );
  }
}
