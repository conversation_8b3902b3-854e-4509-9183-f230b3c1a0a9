class ForgetState {
  final int selectedTab; // 0: 手机号, 1: 邮箱
  final String phoneNumber;
  final String verificationCode;
  final String newPassword;
  final String confirmPassword;
  final bool showNewPassword;
  final bool showConfirmPassword;
  final bool isLoading;
  final String? errorMessage;
  final bool canGetCode;
  final int countdown;
  final String title;
  final bool isSendingCode;

  ForgetState({
    this.selectedTab = 0,
    this.phoneNumber = '',
    this.verificationCode = '',
    this.newPassword = '',
    this.confirmPassword = '',
    this.showNewPassword = false,
    this.showConfirmPassword = false,
    this.isLoading = false,
    this.errorMessage,
    this.canGetCode = true,
    this.countdown = 0,
    this.title = '找回密码',
    this.isSendingCode = false,
  });

  ForgetState init() {
    return ForgetState();
  }

  ForgetState clone() {
    return ForgetState(
      selectedTab: selectedTab,
      phoneNumber: phoneNumber,
      verificationCode: verificationCode,
      newPassword: newPassword,
      confirmPassword: confirmPassword,
      showNewPassword: showNewPassword,
      showConfirmPassword: showConfirmPassword,
      isLoading: isLoading,
      errorMessage: errorMessage,
      canGetCode: canGetCode,
      countdown: countdown,
      title: title,
      isSendingCode: isSendingCode,
    );
  }

  ForgetState copyWith({
    int? selectedTab,
    String? phoneNumber,
    String? verificationCode,
    String? newPassword,
    String? confirmPassword,
    bool? showNewPassword,
    bool? showConfirmPassword,
    bool? isLoading,
    String? errorMessage,
    bool? canGetCode,
    int? countdown,
    String? title,
    bool? isSendingCode,
  }) {
    return ForgetState(
      selectedTab: selectedTab ?? this.selectedTab,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      verificationCode: verificationCode ?? this.verificationCode,
      newPassword: newPassword ?? this.newPassword,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      showNewPassword: showNewPassword ?? this.showNewPassword,
      showConfirmPassword: showConfirmPassword ?? this.showConfirmPassword,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      canGetCode: canGetCode ?? this.canGetCode,
      countdown: countdown ?? this.countdown,
      title: title ?? this.title,
      isSendingCode: isSendingCode ?? this.isSendingCode,
    );
  }

  bool get canSubmit {
    if (selectedTab == 0) {
      return phoneNumber.isNotEmpty &&
          verificationCode.isNotEmpty &&
          newPassword.isNotEmpty &&
          confirmPassword.isNotEmpty &&
          newPassword == confirmPassword;
    } else {
      return phoneNumber.isNotEmpty &&
          verificationCode.isNotEmpty &&
          newPassword.isNotEmpty &&
          confirmPassword.isNotEmpty &&
          newPassword == confirmPassword;
    }
  }
}
