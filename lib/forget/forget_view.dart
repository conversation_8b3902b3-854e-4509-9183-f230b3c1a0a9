import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/appTheme.dart';
import '../utils/toast_util.dart';

import 'forget_bloc.dart';
import 'forget_event.dart';
import 'forget_state.dart';

class ForgetPage extends StatelessWidget {
  final String title;
  const ForgetPage({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => ForgetBloc()..add(InitEvent(title)),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: BlocConsumer<ForgetBloc, ForgetState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ToastUtil.showToast(state.errorMessage!);
          }
        },
        builder: (context, state) {
          final bloc = context.read<ForgetBloc>();

          return SingleChildScrollView(
            child: Column(
              children: [
                // 顶部插画
                SizedBox(
                  height: 280.h,
                  width: double.infinity,
                  child: Image.asset(
                    'images/logoimg.png', // 使用项目中的插画图片
                    fit: BoxFit.contain,
                  ),
                ),

                // 表单内容
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Column(
                    children: [
                      // Tab切换
                      _buildTabBar(context, state, bloc),

                      SizedBox(height: 30.h),

                      // 表单字段
                      _buildFormFields(context, state, bloc),

                      SizedBox(height: 40.h),

                      // 提交按钮
                      _buildSubmitButton(context, state, bloc),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTabBar(
    BuildContext context,
    ForgetState state,
    ForgetBloc bloc,
  ) {
    return Container(
      height: 50.h,
      decoration: BoxDecoration(
        color: AppTheme.bgE,
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () => bloc.add(TabChangedEvent(0)),
              child: AnimatedContainer(
                height: 50.h,
                decoration: BoxDecoration(
                  color:
                      state.selectedTab == 0
                          ? Colors.white
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(25.r),
                  boxShadow:
                      state.selectedTab == 0
                          ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ]
                          : null,
                ),
                duration: const Duration(microseconds: 200),
                curve: Curves.fastEaseInToSlowEaseOut,
                child: Center(
                  child: Text(
                    '手机号',
                    style: TextStyle(
                      color:
                          state.selectedTab == 0
                              ? AppTheme.themeBlue
                              : AppTheme.textBlack_1,
                      fontSize: 16.sp,
                      fontWeight:
                          state.selectedTab == 0
                              ? FontWeight.w600
                              : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () => bloc.add(TabChangedEvent(1)),
              child: AnimatedContainer(
                duration: const Duration(microseconds: 200),
                curve: Curves.fastEaseInToSlowEaseOut,
                height: 50.h,
                decoration: BoxDecoration(
                  color:
                      state.selectedTab == 1
                          ? Colors.white
                          : Colors.transparent,
                  borderRadius: BorderRadius.circular(25.r),
                  boxShadow:
                      state.selectedTab == 1
                          ? [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ]
                          : null,
                ),
                child: Center(
                  child: Text(
                    '邮箱',
                    style: TextStyle(
                      color:
                          state.selectedTab == 1
                              ? AppTheme.themeBlue
                              : AppTheme.textBlack_1,
                      fontSize: 16.sp,
                      fontWeight:
                          state.selectedTab == 1
                              ? FontWeight.w600
                              : FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields(
    BuildContext context,
    ForgetState state,
    ForgetBloc bloc,
  ) {
    return Column(
      children: [
        // 手机号/邮箱输入框
        _buildInputField(
          label: state.selectedTab == 0 ? '手机号码' : '账号',
          hint: state.selectedTab == 0 ? '请输入手机号码' : '请输入账号',
          value: state.phoneNumber,
          onChanged: (value) {
            if (state.selectedTab == 0) {
              bloc.add(PhoneNumberChangedEvent(value));
            } else {
              bloc.add(EmailChangedEvent(value));
            }
          },
        ),

        SizedBox(height: 20.h),

        // 验证码输入框
        _buildVerificationCodeField(context, state, bloc),

        SizedBox(height: 20.h),

        // 设置密码输入框
        _buildPasswordField(
          label: '设置密码',
          hint: '请输入8位以上有效密码',
          value: state.newPassword,
          isVisible: state.showNewPassword,
          onChanged: (value) => bloc.add(NewPasswordChangedEvent(value)),
          onToggleVisibility:
              () => bloc.add(ToggleNewPasswordVisibilityEvent()),
        ),

        SizedBox(height: 20.h),

        // 确认密码输入框
        _buildPasswordField(
          label: '确认密码',
          hint: '请再次输入密码',
          value: state.confirmPassword,
          isVisible: state.showConfirmPassword,
          onChanged: (value) => bloc.add(ConfirmPasswordChangedEvent(value)),
          onToggleVisibility:
              () => bloc.add(ToggleConfirmPasswordVisibilityEvent()),
        ),

        SizedBox(height: 15.h),

        // 密码规则提示
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 12.h),
          decoration: BoxDecoration(
            color: AppTheme.bgE,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Text(
            '密码必须包含大写字母、小写字母、数字、特殊字符其中三种',
            style: TextStyle(color: AppTheme.textBlack_1, fontSize: 12.sp),
          ),
        ),
      ],
    );
  }

  Widget _buildInputField({
    required String label,
    required String hint,
    required String value,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textBlack,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: AppTheme.textBlack_1, fontSize: 14.sp),
            border: UnderlineInputBorder(
              borderSide: BorderSide(color: AppTheme.bgB),
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppTheme.bgB),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppTheme.themeBlue),
            ),
            contentPadding: EdgeInsets.symmetric(vertical: 12.h),
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationCodeField(
    BuildContext context,
    ForgetState state,
    ForgetBloc bloc,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '验证码',
          style: TextStyle(
            color: AppTheme.textBlack,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: TextField(
                onChanged:
                    (value) => bloc.add(VerificationCodeChangedEvent(value)),
                decoration: InputDecoration(
                  hintText: '请输入验证码',
                  hintStyle: TextStyle(
                    color: AppTheme.textBlack_1,
                    fontSize: 14.sp,
                  ),
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppTheme.bgB),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppTheme.bgB),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppTheme.themeBlue),
                  ),
                  contentPadding: EdgeInsets.symmetric(vertical: 12.h),
                ),
              ),
            ),
            SizedBox(width: 15.w),
            SizedBox(
              width: 120,
              height: 40,
              child: ElevatedButton(
                onPressed:
                    state.canGetCode && !state.isSendingCode
                        ? () => bloc.add(GetVerificationCodeEvent())
                        : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      state.canGetCode
                          ? AppTheme.themeBlue
                          : Colors.grey.shade300,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child:
                    state.isSendingCode
                        ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : Text(
                          state.canGetCode ? '获取验证码' : '${state.countdown}s',
                          style: TextStyle(
                            color:
                                state.canGetCode
                                    ? Colors.white
                                    : AppTheme.textBlack_1,
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPasswordField({
    required String label,
    required String hint,
    required String value,
    required bool isVisible,
    required Function(String) onChanged,
    required VoidCallback onToggleVisibility,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textBlack,
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        TextField(
          obscureText: !isVisible,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(color: AppTheme.textBlack_1, fontSize: 14.sp),
            border: UnderlineInputBorder(
              borderSide: BorderSide(color: AppTheme.bgB),
            ),
            enabledBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppTheme.bgB),
            ),
            focusedBorder: UnderlineInputBorder(
              borderSide: BorderSide(color: AppTheme.themeBlue),
            ),
            contentPadding: EdgeInsets.symmetric(vertical: 12.h),
            suffixIcon: IconButton(
              onPressed: onToggleVisibility,
              icon: Icon(
                isVisible ? Icons.visibility : Icons.visibility_off,
                color: AppTheme.textBlack_1,
                size: 20.sp,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(
    BuildContext context,
    ForgetState state,
    ForgetBloc bloc,
  ) {
    return SafeArea(
      child: SizedBox(
        width: double.infinity,
        height: 50.h,
        child: ElevatedButton(
          onPressed:
              state.canSubmit && !state.isLoading
                  ? () => bloc.add(SubmitResetPasswordEvent())
                  : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.themeBlue,
            disabledBackgroundColor: AppTheme.bgB,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            elevation: 0,
          ),
          child:
              state.isLoading
                  ? SizedBox(
                    width: 20.w,
                    height: 20.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : Text(
                    '确认修改',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
        ),
      ),
    );
  }
}
