abstract class ForgetEvent {}

class InitEvent extends ForgetEvent {
  final String title;
  InitEvent(this.title);
}

/// 切换tab
class TabChangedEvent extends ForgetEvent {
  final int tabIndex;
  TabChangedEvent(this.tabIndex);
}

/// 手机号
class PhoneNumberChangedEvent extends ForgetEvent {
  final String phoneNumber;
  PhoneNumberChangedEvent(this.phoneNumber);
}

/// 账号信息
class EmailChangedEvent extends ForgetEvent {
  final String account;
  EmailChangedEvent(this.account);
}

/// 验证码
class VerificationCodeChangedEvent extends ForgetEvent {
  final String code;
  VerificationCodeChangedEvent(this.code);
}

/// 新密码
class NewPasswordChangedEvent extends ForgetEvent {
  final String password;
  NewPasswordChangedEvent(this.password);
}

/// 确认密码
class ConfirmPasswordChangedEvent extends ForgetEvent {
  final String password;
  ConfirmPasswordChangedEvent(this.password);
}

/// 新密码可见性
class ToggleNewPasswordVisibilityEvent extends ForgetEvent {}

/// 确认密码可见性
class ToggleConfirmPasswordVisibilityEvent extends ForgetEvent {}

/// 获取验证码
class GetVerificationCodeEvent extends ForgetEvent {}

/// 提交重置密码
class SubmitResetPasswordEvent extends ForgetEvent {}

/// 定时器加减
class CountdownTickEvent extends ForgetEvent {
  final int countdown;
  CountdownTickEvent(this.countdown);
}

/// 触发定时器操作
class CountdownTimerEvent extends ForgetEvent {
  CountdownTimerEvent();
}

/// 错误消息
class ErrorMessageEvent extends ForgetEvent {
  final String message;
  ErrorMessageEvent(this.message);
}

/// 重置定时器和获取验证码按钮状态
class ResetTimerEvent extends ForgetEvent {
  final bool canGetCode;
  final bool isSendingCode;
  ResetTimerEvent(this.canGetCode,this.isSendingCode);
}

/// 重置密码提交状态
class ResetSubmitEvent extends ForgetEvent {
  final bool isLoading;
  ResetSubmitEvent(this.isLoading);
}