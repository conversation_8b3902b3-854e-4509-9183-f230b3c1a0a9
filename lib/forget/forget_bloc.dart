import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/userInfo.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/common_tools.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

import '../utils/ApiInstance.dart';
import '../utils/AppApplication.dart';
import '../utils/StrUtil.dart';
import '../utils/block_puzzle_captcha.dart';
import '../utils/event_bus_instance.dart';
import '../utils/toast_util.dart';
import 'forget_event.dart';
import 'forget_state.dart';

class ForgetBloc extends Bloc<ForgetEvent, ForgetState> {
  Timer? countTimer;

  int count = 120;

  ForgetBloc() : super(ForgetState().init()) {
    on<InitEvent>(_init);
    on<TabChangedEvent>(_onTabChanged);
    on<PhoneNumberChangedEvent>(_onPhoneNumberChanged);
    on<EmailChangedEvent>(_onEmailChanged);
    on<VerificationCodeChangedEvent>(_onVerificationCodeChanged);
    on<NewPasswordChangedEvent>(_onNewPasswordChanged);
    on<ConfirmPasswordChangedEvent>(_onConfirmPasswordChanged);
    on<ToggleNewPasswordVisibilityEvent>(_onToggleNewPasswordVisibility);
    on<ToggleConfirmPasswordVisibilityEvent>(_onToggleConfirmPasswordVisibility);
    on<GetVerificationCodeEvent>(_onGetVerificationCode);
    on<SubmitResetPasswordEvent>(_onSubmitResetPassword);
    on<CountdownTickEvent>(_onCountdownTick);
    on<ErrorMessageEvent>(_errorMessageEvent);
    on<ResetTimerEvent>(_resetTimerEvent);
    on<ResetSubmitEvent>(_resetSubmitEvent);
    on<CountdownTimerEvent>(_countdownTimerEvent);
  }

  void _init(InitEvent event, Emitter<ForgetState> emit) async {
    emit(state.copyWith(
      title: event.title
    ));
  }

  void _onTabChanged(TabChangedEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      selectedTab: event.tabIndex,
      errorMessage: null,
    ));
  }

  void _onPhoneNumberChanged(PhoneNumberChangedEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      phoneNumber: event.phoneNumber,
      errorMessage: null,
    ));
  }

  void _onEmailChanged(EmailChangedEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      phoneNumber: event.account,
      errorMessage: null,
    ));
  }

  void _onVerificationCodeChanged(VerificationCodeChangedEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      verificationCode: event.code,
      errorMessage: null,
    ));
  }

  void _onNewPasswordChanged(NewPasswordChangedEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      newPassword: event.password,
      errorMessage: null,
    ));
  }

  void _onConfirmPasswordChanged(ConfirmPasswordChangedEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      confirmPassword: event.password,
      errorMessage: null,
    ));
  }

  void _onToggleNewPasswordVisibility(ToggleNewPasswordVisibilityEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(showNewPassword: !state.showNewPassword));
  }

  void _onToggleConfirmPasswordVisibility(ToggleConfirmPasswordVisibilityEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(showConfirmPassword: !state.showConfirmPassword));
  }

  void _onGetVerificationCode(GetVerificationCodeEvent event, Emitter<ForgetState> emit) async {
    if (!state.canGetCode) return;
    if(countTimer != null){
      countTimer?.cancel();
    }

    // Reset count for new timer
    count = 120;

    // Start the countdown timer
    emit(state.copyWith(isSendingCode: true));
    getImageCode();
  }

  void _onSubmitResetPassword(SubmitResetPasswordEvent event, Emitter<ForgetState> emit) async {
    if (!state.canSubmit) return;
    emit(state.copyWith(isLoading: true));
    modifyPassword();
  }

  void _onCountdownTick(CountdownTickEvent event, Emitter<ForgetState> emit) {
    if (event.countdown <= 0) {
      emit(state.copyWith(
        canGetCode: true,
        countdown: 0,
      ));
    } else {
      emit(state.copyWith(countdown: event.countdown,canGetCode: false));
    }
  }

  /// 报错信息提示
  void _errorMessageEvent(ErrorMessageEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(errorMessage: event.message));
  }

  /// 重置倒计时
  void _resetTimerEvent(ResetTimerEvent event, Emitter<ForgetState> emit) {
    if (countTimer != null) {
      countTimer?.cancel();
    }
    emit(state.copyWith(
      canGetCode: true,
      countdown: 120
    ));
  }

  void _resetSubmitEvent(ResetSubmitEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      isLoading: false,
      errorMessage: null,
    ));
  }

  void _countdownTimerEvent(CountdownTimerEvent event, Emitter<ForgetState> emit) {
    emit(state.copyWith(
      isSendingCode: false,
      canGetCode: false
    ));
    countTimer?.cancel();
    int countdown = count;
    countTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      countdown--;
      if (countdown <= 0) {
        timer.cancel();
      }
      add(CountdownTickEvent(countdown));
    });
  }
  // 获取图像验证码接口
  void getImageCode() {
    if (state.selectedTab == 0) {
      if (state.phoneNumber.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }

      if (state.phoneNumber.length != 11) {
        ToastUtil.showWarningToast("请输入正确的手机号");
        return;
      }
      showDialog(
          context: AppApplication.currentContext!,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return BlockPuzzleCaptchaPage(onSuccess: (v,token){
              getChinaCountrySmsCode(v, token);
            }, onFail: (){},close: (){
              if (state.isSendingCode) {
                add(ResetTimerEvent(true,false));
              }
            },);
          });

    } else if (state.selectedTab == 1) {
      if (state.phoneNumber.isEmpty) {
        ToastUtil.showWarningToast("请输入手机号");
        return;
      }
      getImgCode();
    }

  }

  /// 国内手机号获取短信验证码
  void getChinaCountrySmsCode(String pointJson,String token){
    if (state.phoneNumber.isEmpty) {
      ToastUtil.showWarningToast('请输入手机号码');
      return;
    }
    if (state.phoneNumber.length != 11) {
      ToastUtil.showWarningToast('请输入正确的手机号码');
      return;
    }
    if (pointJson.isEmpty) {
      ToastUtil.showWarningToast('请先验证图形验证码');
      return;
    }
    Map<String,String> params = {
      "areaCode": "86",
      "mobile": state.phoneNumber,
      "sendType": "3",
      "type": "2",
      "pointJson": pointJson,
      "token": token,
    };
    ApiInstance().get("${StrUtil.userModule}/sys/Sms/domesticSend",queryParameters: params,errorFunction: (error) {
      ToastUtil.showErrorToast("网络出错了，请稍后再试！");
      add(ResetTimerEvent(true, false));
    }).then((data) {
      if (data != null && data["code"] == 200) {
        ToastUtil.showSuccessToast("验证码获取成功！");
        AppApplication.getCurrentState()?.pop();
      } else if (data != null && data['code']== 12003) {
        eventBus.fire(EventBusInstanceEvent(source: 'checkFail'));
        add(ResetTimerEvent(true, false));
      } else {
        ToastUtil.showWarningToast(data['data']??data["message"]);
        add(ResetTimerEvent(true, false));
      }
    });
  }

  /// 获取邮箱验证码之前先校验邮箱是否存在
  void getImgCode() async {
    if (state.phoneNumber.isEmpty) {
      ToastUtil.showWarningToast("请输入手机号");
      return;
    }
    Map<String,dynamic> data = {
      'mobile': state.phoneNumber,
    };

    final response = await ApiInstance().get("${StrUtil.userModule}/cgz/userApp/sendCaptchaCodeForget",queryParameters: data,errorFunction: (error){
      ToastUtil.showErrorToast(StrUtil.httpError);
    });

    if(response !=null ){
      if(response["code"]==200){
        showDialog<Null>(
          context: AppApplication.currentContext!,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return BlockPuzzleCaptchaPage(
              onSuccess: (v,token){
                getSendEmailCode(pointJson: v,token: token);
              },
              onFail: (){
                // isCodeTap = true;
                // notifyListeners();
              },
              close: () {
                if (state.isSendingCode) {
                  add(ResetTimerEvent(true,false));
                }
              },
            );
          },
        );
      }else if(response['code']==7002){
        showEmailAlert();
      }else {
        ToastUtil.showWarningToast(data["message"]??data["data"]??"");
      }
    }else {
      ToastUtil.showErrorToast(response['message']);
    }
  }

  /// 获取邮箱验证码
  getSendEmailCode({required String pointJson,required String token}) async {
    if (state.phoneNumber.isEmpty) {
      ToastUtil.showWarningToast('请输入手机号码');
      return;
    }
    if (pointJson.isEmpty) {
      ToastUtil.showWarningToast('请先验证图形验证码');
      return;
    }
    Map<String,String> params = {
      "mobile": state.phoneNumber,
      "pointJson": pointJson,
      "token": token
    };
    final response = await ApiInstance().post("${StrUtil.userModule}/cgz/userApp/sendEmailCodeByForget",data: params,errorFunction: (error){
      ToastUtil.showErrorToast(StrUtil.httpError);
      add(ResetTimerEvent(true, false));
    });

    if(response != null){
      if (response["code"] == 200) {
        add(CountdownTimerEvent());
        String emailString = '';
        if (response['data'].isNotEmpty && response['data'].contains('@')) {
          emailString = response['data'].substring(0, response['data'].indexOf("@"));
          emailString = emailString.replaceRange(
              2, emailString.length, "*" * (emailString.length - 2));
          ToastUtil.showSuccessToast(
              "验证码已发送到您注册时绑定的邮箱：$emailString${response['data'].substring(response['data'].indexOf("@"), response['data'].length)}，请注意查收");
        }else{
          ToastUtil.showWarningToast("邮箱格式错误或邮箱不存在，无法发送验证码");
          add(ResetTimerEvent(true, false));
        }
      } else if (response["code"] == 7002) {
        showEmailAlert();
        add(ResetTimerEvent(true, false));
      } else if (response['code'] == 12003) {
        eventBus.fire(EventBusInstanceEvent(source: 'checkFail'));
        add(ResetTimerEvent(true, false));
      } else {
        ToastUtil.showWarningToast(response['data'] ?? response['message']??'');
        add(ResetTimerEvent(true, false));
      }
    }else{
      ToastUtil.showWarningToast('获取邮箱验证码失败，稍后再试');
      add(ResetTimerEvent(true, false));
    }
  }


  void showEmailAlert() {
    showDialog(
        context: AppApplication.currentContext!,
        builder: (context) {
          return Material(
            color: Colors.black.withAlpha(100),
            child: Align(
              alignment: Alignment.center,
              child: Container(
                width: 300.w,
                height: 200.h,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10)
                ),
                child: Column(
                  children: [
                    const Spacer(),
                    Text(
                      '温馨提示',
                      style:
                      TextStyle(fontSize: 20, color: Colors.black,fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                    Text(
                      state.title == '找回密码' ? '您尚未绑定邮箱，请联系公证员进行绑定' : '您尚未绑定邮箱，请进行邮箱绑定',
                      style:
                      TextStyle(fontSize:  15, color: Colors.black,fontWeight: FontWeight.bold),
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            AppApplication.pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10,horizontal: 20),

                            decoration: BoxDecoration(
                                color: Colors.grey,
                                borderRadius: BorderRadius.circular(10)
                            ),
                            child: Text(
                              '取消',
                              style: TextStyle(
                                  fontSize:  15, color: Colors.white),
                            ),
                          ),
                        ),
                        SizedBox(width: 20,),
                        GestureDetector(
                          onTap: () {
                            AppApplication.pop();
                            if(state.title == "找回密码"){
                              canLaunchUrl(Uri(scheme: "tel", path: "**********"));
                            }else{
                             AppApplication.getCurrentState()?.pushNamed(RoutePaths.bindingEmailAccount,arguments: state.phoneNumber);
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.symmetric(vertical: 10,horizontal: 20),
                            decoration: BoxDecoration(
                                color: AppTheme.themeBlue,
                                borderRadius: BorderRadius.circular(10)
                            ),
                            child: Text(
                              '确认',
                              style: TextStyle(
                                  fontSize:  15, color: Colors.white),
                            ),
                          ),
                        )
                      ],
                    ),
                    const Spacer(),
                  ],
                ),
              ),
            ),
          );
        });
  }

  // 修改密码
  void modifyPassword() async {
    if (state.selectedTab == 0) {
      if(state.phoneNumber.isEmpty){
        ToastUtil.showWarningToast('请输入手机号');
        return;
      }
      if(state.phoneNumber.length != 11){
        ToastUtil.showWarningToast('请输入正确的手机号');
        return;
      }
      if(state.verificationCode.isEmpty || state.verificationCode.length != 6){
        ToastUtil.showWarningToast('请输入验证码');
        return;
      }
      if(state.newPassword.isEmpty){
        ToastUtil.showWarningToast('请输入密码');
        return;
      }
      if(state.newPassword.length < 8 || state.newPassword.length > 16){
        ToastUtil.showWarningToast('密码长度为8-16位');
        return;
      }
      if(!CommonTools.checkPassWord(state.newPassword)) {
        ToastUtil.showWarningToast("密码必须包含大写字母、小写字母、数字、特殊字符三种及三种以上");
        return;
      }
      if(state.newPassword != state.confirmPassword){
        ToastUtil.showWarningToast('两次输入的密码不一致');
        return;
      }
      Map<String, String> map = {
        'areaCode':"86",
        "newPassword": CommonTools.generateMd5(state.newPassword),
        "smsCode": state.verificationCode,
        "mobile": state.phoneNumber,
        "type": "2",
      };
      EasyLoading.show();
      final response = await ApiInstance().post("${StrUtil.userModule}/sys/login/forgetPassword",data: map,errorFunction: (error){
        EasyLoading.dismiss();
        ToastUtil.showErrorToast(StrUtil.httpError);
        add(ResetSubmitEvent(false));
      });
      EasyLoading.dismiss();
      if (response != null) {
        if (response["code"] != 200) {
          ToastUtil.showWarningToast(response["message"]??response['data']??response["msg"]??'');
          add(ResetSubmitEvent(false));
          return;
        }
        SpUtil.instance.remove(StrUtil.userInfo);
        AppApplication().userInfoEntity = UserInfoEntity();
        ToastUtil.showOtherToast("密码修改成功");
        AppApplication.getCurrentState()?.pushNamed(RoutePaths.login);
      } else {
        ToastUtil.showWarningToast("修改失败，稍后再试");
        add(ResetSubmitEvent(false));
      }
    } else {
      if (state.phoneNumber.isEmpty) {
        ToastUtil.showWarningToast('请输入手机号');
        return;
      }
      if (state.phoneNumber.length != 11) {
        ToastUtil.showWarningToast('请输入正确的手机号');
        return;
      }
      if (state.verificationCode.isEmpty || state.verificationCode.length != 6) {
        ToastUtil.showWarningToast('请输入验证码');
        return;
      }
      if(state.newPassword.length < 8 || state.newPassword.length > 16){
        ToastUtil.showWarningToast('密码长度为8-16位');
        return;
      }
      if(!CommonTools.checkPassWord(state.newPassword)) {
        ToastUtil.showWarningToast("密码必须包含大写字母、小写字母、数字、特殊字符三种及三种以上");
        return;
      }
      if(state.newPassword != state.confirmPassword){
        ToastUtil.showWarningToast('两次输入的密码不一致');
        return;
      }
      Map<String, dynamic> map = {
        "emailCode": state.verificationCode,
        "mobile": state.phoneNumber,
        "password": CommonTools.generateMd5(state.newPassword),
        'rePassword': CommonTools.generateMd5(state.confirmPassword),
      };
      EasyLoading.show();
      final response = await ApiInstance().post("${StrUtil.userModule}/cgz/userApp/doForgetPassword",data: map,errorFunction: (error){
        EasyLoading.dismiss();
        ToastUtil.showErrorToast(StrUtil.httpError);
        add(ResetSubmitEvent(false));
      });

      EasyLoading.dismiss();
      if (response != null) {
        if (response["code"] != 200) {
          ToastUtil.showWarningToast(response["message"]??response['data']??"");
          add(ResetSubmitEvent(false));
          return;
        }
        SpUtil.instance.remove(StrUtil.userInfo);
        AppApplication().userInfoEntity = UserInfoEntity();
        ToastUtil.showOtherToast("密码修改成功");
        AppApplication.getCurrentState()?.pushNamed(RoutePaths.login);
      } else {
        ToastUtil.showWarningToast("修改失败，稍后再试！");
        add(ResetSubmitEvent(false));
      }
    }
  }

  @override
  Future<void> close() {
    countTimer?.cancel();
    return super.close();
  }
}
