import 'package:bloc_test/models/NotaryOfficeItem.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/appTheme.dart';
import 'apply_information_bloc.dart';
import 'apply_information_event.dart';
import 'apply_information_state.dart';

class ApplyInformationPage extends StatelessWidget {
  final Map<String, dynamic> notarialInfo;
  final String selectDan;
  final List<Children> selectDuo;
  final String purposeName;
  final int isAgent;
  const ApplyInformationPage({
    super.key,
    required this.notarialInfo,
    required this.selectDan,
    required this.selectDuo,
    required this.purposeName,
    required this.isAgent,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (BuildContext context) => ApplyInformationBloc()..add(InitEvent(
            notarialInfo: notarialInfo,
            selectDan: selectDan,
            selectDuo: selectDuo,
            purposeName: purposeName,
            isAgent: isAgent,
          )),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final block = BlocProvider.of<ApplyInformationBloc>(context);
    return BlocBuilder<ApplyInformationBloc, ApplyInformationState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(title: Text('填写申请信息')),
          body: Column(
            children: [
              _buildProgressStepper(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildNotarizationUsageSection(block,state),
                      Offstage(
                        offstage: isAgent == 0,
                        child: Container(
                          margin: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                            color: AppTheme.white,
                            borderRadius: BorderRadius.all(Radius.circular(16.w)),
                            boxShadow: <BoxShadow>[
                              BoxShadow(
                                color: AppTheme.colorD9D9D9.withAlpha(50),
                                blurRadius: 4,
                                offset: Offset(0.0, 10.0),
                              ),
                            ],
                          ),
                          child: MediaQuery.removePadding(
                            context: context,
                            removeBottom: true,
                            removeTop:  true,
                            child: ListView.builder(
                              itemCount: state.sex.length,
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                return _buildApplicantSection(block, state, index);
                              },
                            ),
                          ),
                        ),
                      ),
                      _buildAgentSection(),
                      _buildApplyItem(context),
                    ],
                  ),
                ),
              ),
            ],
          ),
          bottomNavigationBar: _buildBottomButton(block, state),
        );
      },
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButton(
      ApplyInformationBloc block,
      ApplyInformationState state,
      ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed:
            state.isValid ? (){
              block.add(JumpIntoOtherPageEvent());
            } : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.themeBlue,
              foregroundColor: Colors.white,
              disabledBackgroundColor: const Color(0xFFE0E0E0),
              disabledForegroundColor: const Color(0xFF9E9E9E),
              elevation: 0,
            ),
            child: Text(
              '下一步',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressStepper() {
    return Image.asset("images/stepTwo.png", width: double.infinity);
  }

  Widget _buildNotarizationUsageSection(ApplyInformationBloc block,ApplyInformationState  state) {
    return Container(
      padding: EdgeInsets.only(top: 16.h),
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(16.0)),
        boxShadow: [
          BoxShadow(
            color:AppTheme.colorD9D9D9.withAlpha(50),
            offset: Offset(0, 10),
            blurRadius: 4,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionItem(
            title: "公证使用地区",
            placeholder: state.areaModel == null ? "请点击选择" : state.areaModel?.zh??'',
            onTap: () {
              block.add(ChooseAreaTapEvent());
            },
          ),
          SizedBox(height: 16.h),
          _buildSectionItem(
            title: "公证译文语言",
            placeholder: state.languageModel == null ? "请点击选择" : state.languageModel?.zh??'',
            onTap: () {
              block.add(ChooseLanguageTapEvent());
            },
            isHidden: true,
          ),
        ],
      ),
    );
  }

  Widget _buildApplicantSection(
    ApplyInformationBloc block,
    ApplyInformationState state,
    int index,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppTheme.bgE,
            borderRadius:index == 0 ? BorderRadius.only(topLeft: Radius.circular(16.w), topRight: Radius.circular(16.w)) : null,
          ),
          child: Row(
            children: [
              Text(
                "申请人信息",
                style: TextStyle(
                  color: AppTheme.themeBlue,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Spacer(),
              IconButton(
                onPressed: () {
                  block.add(AddApplyUserInformationEvent(index));
                },
                icon: Icon(
                  index == 0
                      ? Icons.add_circle_outline
                      : Icons.remove_circle_outline,
                  color: AppTheme.themeBlue,
                  size: 25.sp,
                ),
              ),
            ],
          ),
        ),
        _itemWidget(block, state, index),
      ],
    );
  }

  Widget _itemWidget(
    ApplyInformationBloc block,
    ApplyInformationState state,
    int index,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildFormField("姓名", "请输入申请人姓名",state.nameControllerList[index], (value) {
          block.add(InputUserInformationEvent(value, index,InputType.name));
        }),
        _buildFormField("身份证号", "请输入身份证号",state.idControllerList[index], (value){
          block.add(InputUserInformationEvent(value, index,InputType.id));
        }),
        _buildDropdownField(block, state, "性别", state.sex[index], index),
        _buildText(
          "出生日期",
          state.birthDayList[index].isEmpty
              ? "请点击选择日期"
              : state.birthDayList[index],
            (){
            block.add(ChooseUserBirthdayEvent(index));
            }
        ),
        _buildFormField(
          "手机号码",
          "请输入手机号",
            state.phoneControllerList[index],
            (value){
              block.add(InputUserInformationEvent(value, index,InputType.phone));
            }
        ),
        _buildMultiLineField(
          "家庭地址",
          "请输入家庭住址",
          state.addressControllerList[index],
          index,
          state,
            (value) {
              block.add(InputUserInformationEvent(value, index,InputType.address));
            }
        ),
      ],
    );
  }

  Widget _buildAgentSection() {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(16.r)),
        boxShadow: <BoxShadow>[
          BoxShadow(
              color: AppTheme.colorD9D9D9.withAlpha(50),
              offset: Offset(0, 10),
              blurRadius: 4),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header
          Offstage(
            offstage: isAgent == 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 15.w),
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.bgE,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.w),
                  topRight: Radius.circular(16.w),
                ),
              ),
              child: Text(
                "代理人信息",
                style: TextStyle(
                  color: AppTheme.themeBlue,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          // Agent form fields would go here
          _buildAgentSectionItem(
            "姓名",
            AppApplication.getInstance().userInfoEntity.userName ?? '',
          ),
          _buildAgentSectionItem(
            "身份证号",
            AppApplication.getInstance().userInfoEntity.idCard ?? '',
          ),
          _buildAgentSectionItem(
            "性别",
            AppApplication.getInstance().userInfoEntity.gender == 1 ? '男' : '女',
          ),
          _buildAgentSectionItem(
            "出生日期",
            AppApplication.getInstance().userInfoEntity.birthday ?? '',
          ),
          _buildAgentSectionItem(
            "手机号码",
            AppApplication.getInstance().userInfoEntity.mobile ?? '',
          ),
          _buildAgentSectionItem(
            "家庭住址",
            AppApplication.getInstance().userInfoEntity.address ?? '',
          ),
        ],
      ),
    );
  }

  Widget _buildAgentSectionItem(String title, String subTitle) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 16.w, horizontal: 16.w),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: title == "家庭住址" ? BorderRadius.only(bottomLeft: Radius.circular(16.w),bottomRight: Radius.circular(16.w)) : (isAgent == 0 && title == "姓名" ? BorderRadius.only(topLeft: Radius.circular(16.w),topRight: Radius.circular(16.w)) : null),
        border: Border(bottom: BorderSide(color: AppTheme.bgB, width: 1.w)),
      ),
      child: Row(
        children: [
          Text(
            title,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
          ),
          SizedBox(width: 20.w,),
          Expanded(
            child: Text(
              subTitle,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.right,
              maxLines: 5,
              style: TextStyle(fontSize: 14.sp, color: AppTheme.darkGrey),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionItem({
    required String title,
    required String placeholder,
    required VoidCallback onTap,
    bool isHidden = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration:
            isHidden
                ? null
                : BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: AppTheme.bgB, width: 1),
                  ),
                ),
        child: Row(
          children: [
            Text(
              title,
              style: TextStyle(fontSize: 16.sp, color: Colors.black87),
            ),
            Spacer(),
            Text(
              placeholder,
              style: TextStyle(fontSize: 16.sp, color: Colors.grey[600]),
            ),
            SizedBox(width: 8.w),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16.sp),
          ],
        ),
      ),
    );
  }

  Widget _buildFormField(
    String label,
    String placeholder,
    TextEditingController controller,
    ValueChanged<String> onChanged,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.w),
      decoration: BoxDecoration(
        color: AppTheme.white,
        border: Border(bottom: BorderSide(color: AppTheme.bgB, width: 1.0)),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(fontSize: 16.sp, color: Colors.black87),
            ),
          ),
          Expanded(
            child: TextField(
              controller: controller,
              textAlign: TextAlign.right,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: placeholder,
                hintStyle: TextStyle(fontSize: 16.sp, color: Colors.grey),
              ),
              onChanged: (value) {
                onChanged.call(value);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildText(String label, String placeholder,Function onChanged) {
    return GestureDetector(
      onTap: (){
        onChanged();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 15.w),
        decoration: BoxDecoration(
          color: AppTheme.white,
          border: Border(bottom: BorderSide(width: 1, color: AppTheme.bgB)),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 80.w,
              child: Text(
                label,
                style: TextStyle(fontSize: 16.sp, color: Colors.black87),
              ),
            ),
            Expanded(
              child: Text(
                placeholder,
                textAlign: TextAlign.right,
                style: TextStyle(
                  fontSize: 16.sp,
                  color: placeholder == "请点击选择日期" ? AppTheme.darkGrey.withAlpha(100): AppTheme.darkerText,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownField(
    ApplyInformationBloc block,
    ApplyInformationState state,
    String label,
    String value,
    int index,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.w),
      decoration: BoxDecoration(
        color: AppTheme.white,
        border: Border(bottom: BorderSide(color: AppTheme.bgB, width: 1)),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 80.w,
            child: Text(
              label,
              style: TextStyle(fontSize: 16.sp, color: Colors.black87),
            ),
          ),
          const Spacer(),
          DropdownButton(
            value: state.sex[index],
            underline: Container(
              height: 0.0,
              color: Colors.green.withOpacity(0.7),
            ),
            items: [
              DropdownMenuItem(
                value: "男",
                child: Text(
                  '男',
                  style: TextStyle(fontSize: 16.sp, color: AppTheme.darkGrey),
                ),
              ),
              DropdownMenuItem(
                value: "女",
                child: Text(
                  '女',
                  style: TextStyle(fontSize: 16.sp, color: AppTheme.darkGrey),
                ),
              ),
            ],
            onChanged: (value) {
              block.add(ChooseSexEvent(value as String, index));
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMultiLineField(
    String label,
    String placeholder,
      TextEditingController controller,
      int index,
      ApplyInformationState state,
      ValueChanged<String> onChanged,
  ) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.w),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius:index == state.sex.length-1?BorderRadius.only(bottomLeft: Radius.circular(16.w),bottomRight: Radius.circular(16.w)): null,
        border: index == state.sex.length-1 ? null : Border(bottom: BorderSide(width: 1, color: AppTheme.bgB)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: TextStyle(fontSize: 16.sp, color: Colors.black87)),
          SizedBox(height: 8.h),
          TextField(
            controller: controller,
            textAlign: TextAlign.left,
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: placeholder,
              hintStyle: TextStyle(fontSize: 16.sp, color: Colors.grey),
            ),
            onChanged: (value) {
              onChanged(value);
            },
          ),
        ],
      ),
    );
  }

  /// 办证事项
  Widget _buildApplyItem(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.all(
          Radius.circular(10.w),
        ),
        boxShadow: <BoxShadow>[
          BoxShadow(
            color: AppTheme.colorD9D9D9.withAlpha(50),
            offset: Offset(0, 10.0),
            blurRadius: 4.0,
          ),
        ]
      ),
      padding: EdgeInsets.only(
        left: 16.w,
        right: 16.w,
        bottom: 10.h,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16),
            child: Text(
              "办理公证事项",
              style: TextStyle(
                fontSize: 16.sp,
                color: AppTheme.themeBlue,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          MediaQuery.removePadding(
            context: context,
            removeTop: true,
            removeBottom:  true,
            child: ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: selectDuo.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: Text(selectDuo[index].name ?? ''),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
