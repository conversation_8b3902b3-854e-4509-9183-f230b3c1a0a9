import 'package:bloc_test/apply_information/apply_information_state.dart';
import 'package:bloc_test/models/AreaModel.dart';
import 'package:bloc_test/models/LanguageModel.dart';

import '../models/NotaryOfficeItem.dart';

abstract class ApplyInformationEvent {}

class InitEvent extends ApplyInformationEvent {
  final Map<String, dynamic> notarialInfo;
  final String selectDan;
  final List<Children> selectDuo;
  final String purposeName;
  final int isAgent;
  InitEvent({
    required this.notarialInfo,
    required this.selectDan,
    required this.selectDuo,
    required this.purposeName,
    required this.isAgent,
  });
}

class ChooseSexEvent extends ApplyInformationEvent {
  final String sex;
  final int index;
  ChooseSexEvent(this.sex,this.index);
}

class AddApplyUserInformationEvent extends ApplyInformationEvent {
  final int index;
  AddApplyUserInformationEvent(this.index);
}

class ChooseAreaTapEvent extends ApplyInformationEvent {}
class ChooseLanguageTapEvent extends ApplyInformationEvent {}
class ChooseAreaSuccessEvent extends ApplyInformationEvent {
  final AreaModel area;
  ChooseAreaSuccessEvent(this.area);
}

class ChooseLanguageSuccessEvent extends ApplyInformationEvent {
  final LanguageModel language;
  ChooseLanguageSuccessEvent(this.language);
}

class JumpIntoOtherPageEvent extends ApplyInformationEvent {}
class InputUserInformationEvent extends ApplyInformationEvent {
  final String valueStr;
  final int index;
  final InputType type;
  InputUserInformationEvent(this.valueStr,this.index,this.type);
}

class ChooseUserBirthdayEvent extends ApplyInformationEvent {
  final int index;
  ChooseUserBirthdayEvent(this.index);
}