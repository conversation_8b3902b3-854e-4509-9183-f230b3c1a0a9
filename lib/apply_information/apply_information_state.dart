import 'package:bloc_test/models/AreaModel.dart';
import 'package:bloc_test/models/LanguageModel.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:flutter/cupertino.dart';

import '../models/NotaryOfficeItem.dart';

enum InputType { name, id, phone, address }

class ApplyInformationState {
  Map<String, dynamic>? notarialInfo;
  String? selectDan;
  List<Children>? selectDuo;
  String? purposeName;
  int? isAgent;

  List<String> nameList = [];
  List<String> idList = [];
  List<String> sex = ["男"];
  List<String> birthDayList = [""];
  List<String> phoneList = [];
  List<String> addressList = [];

  List<TextEditingController> nameControllerList = [TextEditingController()];
  List<TextEditingController> idControllerList = [TextEditingController()];
  List<TextEditingController> phoneControllerList = [TextEditingController()];
  List<TextEditingController> addressControllerList = [TextEditingController()];

  List<AreaModel> areaList = [];
  List<LanguageModel> languageList = [];
  AreaModel? areaModel;
  LanguageModel? languageModel;

  String? latLng;
  String? ipAddress;

  ApplyInformationState init() {
    return ApplyInformationState()
      ..addressList = [""]
      ..idList = [""]
      ..sex = ["男"]
      ..birthDayList = [""]
      ..phoneList = [""]
      ..nameControllerList = [TextEditingController()]
      ..idControllerList = [TextEditingController()]
      ..phoneControllerList = [TextEditingController()]
      ..addressControllerList = [TextEditingController()]
      ..areaList = []
      ..languageList = []
      ..areaModel = null
      ..languageModel = null
      ..notarialInfo = null
      ..selectDan = null
      ..selectDuo = null
      ..isAgent = null
      ..purposeName = null
      ..latLng = null
      ..ipAddress = null
      ..nameList = [""];
  }

  ApplyInformationState clone() {
    return ApplyInformationState()
      ..nameList = nameList
      ..idList = idList
      ..sex = sex
      ..birthDayList = birthDayList
      ..phoneList = phoneList
      ..phoneControllerList = phoneControllerList
      ..nameControllerList = nameControllerList
      ..idControllerList = idControllerList
      ..addressControllerList = addressControllerList
      ..areaModel = areaModel
      ..languageModel = languageModel
      ..areaList = areaList
      ..languageList = languageList
      ..notarialInfo = notarialInfo
      ..selectDuo = selectDuo
      ..selectDan = selectDan
      ..purposeName = purposeName
      ..isAgent = isAgent
      ..latLng = latLng
      ..ipAddress = ipAddress
      ..addressList = addressList;
  }

  bool get isValid {
    if (isAgent == 0) {
      return areaModel != null && languageModel != null;
    } else {
      logger.d("areaModel------${areaModel?.toJson()}");
      logger.d("languageModel-------${languageModel?.toJson()}");
      return idList.every((value) {
            logger.d("idList--------$value");
            return value.isNotEmpty;
          }) &&
          phoneList.every((value) {
            logger.d("phoneList--------$value");
            return value.isNotEmpty;
          }) &&
          addressList.every((value) {
            logger.d("addressList--------$value");
            return value.isNotEmpty;
          }) &&
          nameList.every((value) {
            logger.d("nameList--------$value");
            return value.isNotEmpty;
          }) &&
          sex.every((value) {
            logger.d("sex--------$value");
            return value.isNotEmpty;
          }) &&
          birthDayList.every((value) {
            logger.d("birthDayList--------$value");
            return value.isNotEmpty;
          }) &&
          areaModel != null &&
          languageModel != null;
    }
  }
}
