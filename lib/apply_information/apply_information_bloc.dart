import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/AreaModel.dart';
import 'package:bloc_test/models/LanguageModel.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/amap_location_utils.dart';
import 'package:bloc_test/utils/common_tools.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_datetime_picker_plus/flutter_datetime_picker_plus.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:permission_handler/permission_handler.dart';

import '../widgets/AlertView.dart';
import 'apply_information_event.dart';
import 'apply_information_state.dart';

class ApplyInformationBloc extends Bloc<ApplyInformationEvent, ApplyInformationState> {
  ApplyInformationBloc() : super(ApplyInformationState().init()) {
    on<InitEvent>(_init);
    on<ChooseSexEvent>(_chooseSexEvent);
    on<AddApplyUserInformationEvent>(_addApplyUserInformationEvent);
    on<ChooseAreaTapEvent>(_chooseAreaTapEvent);
    on<ChooseAreaSuccessEvent>(_chooseAreaSuccessEvent);
    on<ChooseLanguageTapEvent>(_chooseLanguageTapEvent);
    on<ChooseLanguageSuccessEvent>(_chooseLanguageSuccessEvent);
    on<JumpIntoOtherPageEvent>(_jumpIntoOtherPageEvent);
    on<InputUserInformationEvent>(_inputUserInformationEvent);
    on<ChooseUserBirthdayEvent>(_chooseUserBirthDayEvent);
  }

  void _init(InitEvent event, Emitter<ApplyInformationState> emit) async {
    List<AreaModel> tempAreaList = await getCountryArea();
    List<LanguageModel> tempLanguageList = await getCountryLanguage();
    emit(state.clone()
    ..areaList = tempAreaList
    ..languageList = tempLanguageList
    ..notarialInfo = event.notarialInfo
    ..selectDan = event.selectDan
    ..selectDuo = event.selectDuo
    ..purposeName = event.purposeName
    ..isAgent = event.isAgent);
    getLocation();
  }
  void _chooseSexEvent(ChooseSexEvent event, Emitter<ApplyInformationState> emit) async {
    emit(state.clone()..sex[event.index] = event.sex);
  }
  void _addApplyUserInformationEvent(AddApplyUserInformationEvent event, Emitter<ApplyInformationState> emit) async {
    if (event.index == 0) {
       if (state.sex.length > 9) {
         ToastUtil.showToast(
             "当前申请人最多可添加10个人");
       } else {
         emit(state.clone()..nameList.add('')
           ..idList.add('')
           ..sex.add("男")
           ..birthDayList.add("")
           ..phoneList.add('')
           ..addressList.add('')
           ..nameControllerList.add(TextEditingController())
           ..phoneControllerList.add(TextEditingController())
           ..idControllerList.add(TextEditingController())
           ..addressControllerList.add(TextEditingController()));
       }
    } else {
      emit(state.clone()..nameList.removeAt(event.index)
        ..idList.removeAt(event.index)
        ..sex.removeAt(event.index)
        ..birthDayList.removeAt(event.index)
        ..phoneList.removeAt(event.index)
        ..addressList.removeAt(event.index)
        ..nameControllerList.removeAt(event.index)
        ..idControllerList.removeAt(event.index)
        ..phoneControllerList.removeAt(event.index)
        ..addressControllerList.removeAt(event.index));
    }
  }
  void _chooseAreaTapEvent(ChooseAreaTapEvent event, Emitter<ApplyInformationState> emit) async {
    changeCountry();
  }

  void _chooseAreaSuccessEvent(ChooseAreaSuccessEvent event, Emitter<ApplyInformationState> emit) async {
    emit(state.clone()..areaModel = event.area);
  }

  void _chooseLanguageTapEvent(ChooseLanguageTapEvent event, Emitter<ApplyInformationState> emit) async {
    changeLanguage();
  }

  void _chooseLanguageSuccessEvent(ChooseLanguageSuccessEvent event, Emitter<ApplyInformationState> emit) async {
    emit(state.clone()..languageModel = event.language);
  }

  void _inputUserInformationEvent(InputUserInformationEvent event, Emitter<ApplyInformationState> emit)async {
    switch (event.type) {
      case InputType.name:
        emit(state.clone()..nameList[event.index] = event.valueStr);
        break;
      case InputType.id:
        if (event.valueStr.isNotEmpty && RegexUtil.isIDCard18(event.valueStr)) {
          emit(state.clone()..sex[event.index] = CommonTools.getSexFromCardId(event.valueStr)
            ..birthDayList[event.index] = CommonTools.getBirthDayFromCardId(event.valueStr)
          ..idList[event.index] = event.valueStr);
        } else {
          emit(state.clone()..sex[event.index] = '男'
            ..birthDayList[event.index] = ''
          ..idList[event.index] = event.valueStr);
        }
        break ;
      case InputType.address:
        emit(state.clone()..addressList[event.index] = event.valueStr);
        break;
        case InputType.phone:
          emit(state.clone()..phoneList[event.index] = event.valueStr);
        break;
    }
  }

  void _chooseUserBirthDayEvent(ChooseUserBirthdayEvent event, Emitter<ApplyInformationState> emit) async {
    // emit(state.clone()..birthDayList[event.index] = event.birthday);
     DateTime date = (await DatePicker.showDatePicker(
        AppApplication.currentContext!,
        // 是否展示顶部操作按钮
        showTitleActions: true,
        minTime: DateTime(1900, 11, 31),
        maxTime:  DateTime.now(), onChanged: (date) {

        },
        // 确定事件
        onConfirm: (date) {
          return date;
        },
        // 当前时间
        currentTime: DateTime.now(),
        // 语言
        locale: LocaleType.zh)) as DateTime;
     emit(state.clone()..birthDayList[event.index] = "$date".toString().substring(0, 11));
  }

  void _jumpIntoOtherPageEvent(JumpIntoOtherPageEvent event, Emitter<ApplyInformationState> emit) async {
    await uploadOrder();
  }

  /// 获取定位信息
  getLocation() async {
    if(!await Permission.location.status.isGranted){
      CommonTools.showCustomToast(
          context: AppApplication.currentContext!,
          titleText: "定位权限使用说明：",
          subTitleText: "用于获取当前位置信息",
          time: 2
      );
    }
    if (await Permission.location.request().isGranted) {
      AmapLocationUtils.getStart((result) {
        state.latLng = "${result["longitude"]},${result["latitude"]}";
      }, errorCallBack: (error) async {
        state.ipAddress = await CommonTools.getDeviceIPAddress();
      });
    } else {
      state.ipAddress = await CommonTools.getDeviceIPAddress();
      CommonTools.showPermissionDialog(str: "定位权限");
    }
  }
  /// 获取公证区域
   getCountryArea()async{
    state.areaList.clear();
    state.languageList.clear();
   final response = await ApiInstance().post("${StrUtil.notaryModule}/sys/countryArea/selectAll",data: {},errorFunction: (error){

    });
   if (response != null && response["code"] == 200) {
     if (response["items"] != null) {
       List temp = response["items"];
       List<AreaModel> tempList = [];
       for (var item in temp) {
         AreaModel model = AreaModel.fromJson(item);
         tempList.add(model);
       }
       return tempList;
     } else {
       return [];
     }
   } else {
     return [];
   }
  }

  /// 获取公证语言
  getCountryLanguage()async {
    state.areaList.clear();
    state.languageList.clear();
    final response = await ApiInstance().post("${StrUtil.notaryModule}/sys/countryLanguage/selectAll",data: {},errorFunction: (error){

    });
    if (response != null && response["code"] == 200) {
      if (response["items"] != null) {
        List temp = response["items"];
        List<LanguageModel> tempList = [];
        for (var item in temp) {
          LanguageModel model = LanguageModel.fromJson(item);
          tempList.add(model);
        }
        return tempList;
      } else {
        return [];
      }
    } else {
      return [];
    }
 }


  /// 选择公证区域弹窗
  Future<void> changeCountry() async {
    List<String> nameList = [];
    for (var item in state.areaList) {
      nameList.add(item.zh??'');
    }
    showDialog(
        context: AppApplication.currentContext!,
        useSafeArea: false,
        builder: (context) {
          return BottomAlertSearchList(
            holderString: "选择公证使用地区",
            selectValueCallBack: (value) {
              for (var element in state.areaList) {
                if (element.zh == value) {
                  add(ChooseAreaSuccessEvent(element));
                }
              }
            },
            dataSource: nameList,
          );
        });
  }

  /// 选择语言弹窗
  Future<void> changeLanguage() async {
    List<String> nameList = [];
    for (var item in state.languageList) {
      nameList.add(item.name??'');
    }
    showDialog(
        context: AppApplication.currentContext!,
        useSafeArea: false,
        builder: (context) {
          return BottomAlertSearchList(
            holderString: "请选择公证译文语言",
            selectValueCallBack: (value) {
              for (var element in state.languageList) {
                if (element.name == value) {
                  add(ChooseLanguageSuccessEvent(element));
                }
              }
            },
            dataSource: nameList,
          );
        });
  }

  /// 提交订单

  uploadOrder() async {
    if (!state.isValid) {
      return;
    }

    List itemList = [];
    state.selectDuo?.forEach((element) {
      itemList.add({
        "notaryItemId": element.unitGuid,
        "price": element.price,
        "notaryItemName": element.name,
        "notaryNum": 1,
        "orderId": ""
      });
    });
    DateTime now = DateTime.now();
    final toDay = "${now.year}-${now.month}-${now.day}";
    Map<String,dynamic> params = {};
    if (state.isAgent == 1) {
      List<Map<String,dynamic>> agentList = [];
      agentList.add({
        "name": AppApplication.getInstance().userInfoEntity.userName,
        "relationShip": "",
        "mobile": AppApplication.getInstance().userInfoEntity.mobile,
        "idCard": AppApplication.getInstance().userInfoEntity.idCard,
        "birthday": AppApplication.getInstance().userInfoEntity.birthday,
        "gender": AppApplication.getInstance().userInfoEntity.gender.toString(),
        "address": AppApplication.getInstance().userInfoEntity.address,
        "orderId": "",
        "principal": ""
      });
      state.sex.asMap().forEach((index, element) {
        agentList.add({
          "name": state.nameList[index],
          "relationShip": "",
          "mobile": state.phoneList[index],
          "idCard": state.idList[index],
          "birthday": state.birthDayList[index],
          "gender": element == "男" ? "1" : "2",
          "address": state.addressList[index],
          "orderId": "",
          "principal": ""
        });
      });
      params = {
        //"userId":userViewModel.unitGuid,
        "name":  AppApplication.getInstance().userInfoEntity.userName,
        "useArea": state.areaModel?.zh,
        "useLanguage": state.languageModel?.name,
        "purposeName": state.purposeName,
        "notaryId": state.notarialInfo?["unitGuid"],
        "isDaiBan": 1,
        "notaryForm": 1,
        "terminalType": 1,
        "description": "",
        "notaryitems": JsonUtil.encodeObj(itemList),
        "applyUsers": JsonUtil.encodeObj(agentList),
        "videolog":
        JsonUtil.encodeObj({"planDate": toDay, "videoDate": toDay}),
      };
    } else {
      params = {
        "name": AppApplication.getInstance().userInfoEntity.userName,
        "useArea": state.areaModel?.zh,
        "useLanguage": state.languageModel?.name,
        "purposeName": state.purposeName,
        "notaryId": state.notarialInfo?["unitGuid"],
        "isDaiBan": 0,
        "notaryForm": 1,
        "terminalType": 1,
        "description": "",
        "notaryitems": JsonUtil.encodeObj(itemList),
        "applyUsers": JsonUtil.encodeObj([
          {
            "name": AppApplication.getInstance().userInfoEntity.userName,
            "relationShip": "",
            "mobile": AppApplication.getInstance().userInfoEntity.mobile,
            "idCard": AppApplication.getInstance().userInfoEntity.idCard,
            "birthday": AppApplication.getInstance().userInfoEntity.birthday,
            "gender": AppApplication.getInstance().userInfoEntity.gender,
            "address": AppApplication.getInstance().userInfoEntity.address,
            "orderId": ""
          }
        ]),
        "videolog":
        JsonUtil.encodeObj({"planDate": toDay, "videoDate": toDay}),
      };
    }
    EasyLoading.show();
   final response = await ApiInstance().post("${StrUtil.notaryModule}/cgz/notaryorder/add",data: params,errorFunction: (error){
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    });
    EasyLoading.dismiss();
   if (response != null && response['code'] == 200) {
     AppApplication.getCurrentState()?.pushNamed(RoutePaths.confirmInformation,arguments: {
    "orderId": response["unitGuid"],
    });
     CommonTools.uploadLocationOrIpAddress({
       'busiId':response["unitGuid"],
       'busiType': 1,
       "idCard":AppApplication.getInstance().userInfoEntity.idCard,
       'ip': state.ipAddress,
       "longitude":state.latLng != null && state.latLng!.isNotEmpty ? state.latLng!.split(',')[0] : "",
       "latitude":state.latLng != null && state.latLng!.isNotEmpty ? state.latLng!.split(',')[1] : ""
     });
   }
  }

  @override
  Future<void> close() {
    for (var controller in state.nameControllerList) {
      controller.dispose();
    }
    for (var controller in state.idControllerList) {
      controller.dispose();
    }
    for (var controller in state.phoneControllerList) {
      controller.dispose();
    }

    for (var controller in state.addressControllerList) {
      controller.dispose();
    }
    return super.close();
  }
}
