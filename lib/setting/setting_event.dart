import 'package:flutter/material.dart';
import 'setting_state.dart';

abstract class SettingEvent {}

class InitEvent extends SettingEvent {}

class SettingItemTapEvent extends SettingEvent {
  final SettingEventType type;
  SettingItemTapEvent(this.type);
}

class CancelAccountEvent extends SettingEvent {
  final String reason;
  final String password;
  CancelAccountEvent({required this.reason, required this.password});
}

class ShowCancelAccountDialogEvent extends SettingEvent {}

class HideCancelAccountDialogEvent extends SettingEvent {}