import 'dart:convert';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/userInfo.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:crypto/crypto.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../utils/StrUtil.dart';
import '../utils/router.dart';
import 'setting_event.dart';
import 'setting_state.dart';
import 'package:path_provider/path_provider.dart' show getApplicationDocumentsDirectory;

class SettingBloc extends Bloc<SettingEvent, SettingState> {
  SettingBloc() : super(SettingState().init()) {
    on<InitEvent>(_init);
    on<SettingItemTapEvent>(_settingItemTapEvent);
    on<CancelAccountEvent>(_cancelAccount);
    on<ShowCancelAccountDialogEvent>(_showCancelAccountDialog);
    on<HideCancelAccountDialogEvent>(_hideCancelAccountDialog);
  }

  void _init(InitEvent event, Emitter<SettingState> emit) async {
    final version = await PackageInfo.fromPlatform();
    final cacheString = await getCache();
    emit(state.clone()
      ..version = version.version
      ..cacheSize = cacheString);
  }
  
  void _settingItemTapEvent(SettingItemTapEvent event, Emitter<SettingState> emit) async {
    switch (event.type) {
      case SettingEventType.clearCache:
        final cacheStr = await clearApplicationCache();
        emit(state.clone()..cacheSize = cacheStr);
        break;
        case SettingEventType.lookPrivacy:
          AppApplication.getCurrentState()?.pushNamed(RoutePaths.webViewWidget,arguments:  {
            'url': StrUtil.privacyPolicy,
            'title': '隐私政策',
          });
          break;
          case SettingEventType.lookService:
            AppApplication.getCurrentState()?.pushNamed(RoutePaths.userService);
            break;
            case SettingEventType.forgetPsd:
              AppApplication.getCurrentState()?.pushNamed(RoutePaths.modifyPsd,arguments: "重置密码");
              break;
              case SettingEventType.logout:
                await logout();
                break;
                case SettingEventType.cancelAccount:
                  AppApplication.getCurrentState()?.pushNamed(RoutePaths.cancelAccount);
                  break;
    }
  }

  void _showCancelAccountDialog(ShowCancelAccountDialogEvent event, Emitter<SettingState> emit) {
    emit(state.clone()..isCancelAccountDialogVisible = true);
  }

  void _hideCancelAccountDialog(HideCancelAccountDialogEvent event, Emitter<SettingState> emit) {
    emit(state.clone()..isCancelAccountDialogVisible = false);
  }

  void _cancelAccount(CancelAccountEvent event, Emitter<SettingState> emit) async {
    emit(state.clone()..isCancelingAccount = true..cancelAccountError = null);
    
    try {
      final response = await ApiInstance().post(
        "${StrUtil.userModule}/cgz/user/userToCancellation",
        queryParameters: {
          'reason': event.reason,
          'password': md5.convert(Utf8Encoder().convert(event.password)),
        },
        errorFunction: (error) {
          ToastUtil.showErrorToast(StrUtil.httpError);
        }
      );
      
      if (response["code"] == 200) {
        // 注销成功，清除用户数据并跳转到登录页
        SpUtil.instance.remove(StrUtil.userInfo);
        AppApplication().userInfoEntity = UserInfoEntity();
        ToastUtil.showSuccessToast("账号注销成功");
        AppApplication.getCurrentState()?.pushNamedAndRemoveUntil(RoutePaths.login, (route) => false);
      } else {
        emit(state.clone()..cancelAccountError = response["message"] ?? "注销失败，请重试");
      }
    } catch (e) {
      emit(state.clone()..cancelAccountError = "网络错误，请检查网络连接");
    } finally {
      emit(state.clone()..isCancelingAccount = false);
    }
  }

  /// 获取缓存
  Future<double> loadApplicationCache() async {
    try {
      /// 获取文件夹
      Directory directory = await getApplicationDocumentsDirectory();

      /// 检查目录是否存在
      if (!await directory.exists()) {
        logger.d("Documents directory does not exist: $directory");
        return 0.0;
      }

      /// 获取缓存大小
      double value = await getTotalSizeOfFilesInDir(directory);
      return value;
    } catch (e) {
      logger.d("Error loading application cache: $e");
      return 0.0;
    }
  }

  /// 循环计算文件的大小（递归）
  Future<double> getTotalSizeOfFilesInDir(final FileSystemEntity file) async {
    try {
      if (file is File) {
        if (await file.exists()) {
          int length = await file.length();
          return double.parse(length.toString());
        }
        return 0.0;
      }
      if (file is Directory) {
        if (!await file.exists()) {
          logger.d("Directory does not exist: $file");
          return 0.0;
        }
        
        try {
          final List<FileSystemEntity> children = file.listSync();
          double total = 0;
          if (children.isNotEmpty) {
            for (int i = 0; i < children.length; i++) {
              FileSystemEntity child = children[i];
              total += await getTotalSizeOfFilesInDir(child);
            }
          }
          return total;
        } catch (e) {
          logger.d("Error listing directory contents: $file, error: $e");
          return 0.0;
        }
      }
      return 0.0;
    } catch (e) {
      logger.d("Error in getTotalSizeOfFilesInDir: $e");
      return 0.0;
    }
  }

  /// 缓存大小格式转换
  String formatSize(double value) {
    List<String> unitArr = ['B',"K","M","G"];
    int index = 0;
    while (value > 1024) {
      index++;
      value = value / 1024;
    }
    String size = value.toStringAsFixed(2);
    return size + unitArr[index];
  }

  /// 删除缓存
  Future<String?> clearApplicationCache() async {
    Directory directory = await getApplicationDocumentsDirectory();
    logger.d("缓存路径$directory");
    //删除缓存目录
    await deleteDirectory(directory);
    final cacheStr = await getCache();
    return cacheStr;
  }

  /// 递归方式删除目录
  Future<Null> deleteDirectory(FileSystemEntity file) async {
    try {
      if (!await file.exists()) {
        logger.d("File/Directory does not exist: $file");
        return;
      }
      
      if (file is Directory) {
        try {
          final List<FileSystemEntity> children = file.listSync();
          for (final FileSystemEntity child in children) {
            await deleteDirectory(child);
          }
        } catch (e) {
          logger.d("Error listing directory contents for deletion: $file, error: $e");
          return;
        }
      }
      await file.delete();
    } catch (e) {
      logger.d("Error deleting file/directory: $file, error: $e");
    }
  }

  Future<String> getCache() async {
    try {
      double value = await loadApplicationCache();
      final cacheStr = formatSize(value);
      logger.d('获取app缓存: $cacheStr');
      return cacheStr;
    } catch (e) {
      logger.d('Error getting cache: $e');
      return '0B';
    }
  }

  /// 用户退出登录
  Future<void> logout() async {
    final response = await ApiInstance().post("${StrUtil.userModule}/sys/login/loginOut",data: {},errorFunction: (error){
      ToastUtil.showErrorToast(StrUtil.httpError);
    });
    if(response["code"] == 200){
      SpUtil.instance.remove(StrUtil.userInfo);
      AppApplication().userInfoEntity = UserInfoEntity();
      AppApplication.getCurrentState()?.pushNamed(RoutePaths.login);
    }
  }
}
