import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'setting_bloc.dart';
import 'setting_event.dart';
import 'setting_state.dart';

class SettingPage extends StatelessWidget {
  const SettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => SettingBloc()..add(InitEvent()),
      child: _buildPage(context),
    );
  }

  Widget _buildPage(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('系统设置')),
      body: BlocBuilder<SettingBloc, SettingState>(
        builder: (context, state) {
          final block = context.read<SettingBloc>();
          return SingleChildScrollView(
            child: Column(
              children: [
                const SizedBox(height: 8),
                Card(
                  margin: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                  shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.zero,
                  ),
                  elevation: 0,
                  child: Column(
                    children: [
                      _buildSettingItem('隐私政策', onTap: () {
                       block.add(SettingItemTapEvent(SettingEventType.lookPrivacy));
                      }),
                      _buildSettingItem('服务协议', onTap: () {
                        block.add(SettingItemTapEvent(SettingEventType.lookService));
                      }),
                      _buildSettingItem(
                        '清理缓存',
                        value: state.cacheSize??'',
                        valueColor: Colors.green,
                        onTap: () {
                          block.add(SettingItemTapEvent(SettingEventType.clearCache));
                        },
                      ),
                      _buildSettingItem(
                        '版本号',
                        value: state.version??'',
                        valueColor: Colors.green,
                        onTap: () {},
                      ),
                      _buildSettingItem('重置密码', onTap: () {
                        block.add(SettingItemTapEvent(SettingEventType.forgetPsd));
                      }),
                    ],
                  ),
                ),
                const SizedBox(height: 32),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24.0,
                    vertical: 8,
                  ),
                  child: Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: () {
                            block.add(SettingItemTapEvent(SettingEventType.cancelAccount));
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            '注销登录账号',
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        height: 48,
                        child: ElevatedButton(
                          onPressed: () {
                            block.add(SettingItemTapEvent(SettingEventType.logout));
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.themeBlue,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            '退出当前账号',
                            style: TextStyle(fontSize: 16, color: Colors.white),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSettingItem(
    String title, {
    String? value,
    Color? valueColor,
    VoidCallback? onTap,
  }) {
    return Column(
      children: [
        ListTile(
          title: Text(title, style: const TextStyle(fontSize: 16)),
          trailing:
              value != null
                  ? Text(
                    value,
                    style: TextStyle(
                      color: valueColor ?? Colors.black,
                      fontSize: 15,
                    ),
                  )
                  : const Icon(Icons.chevron_right, color: Colors.grey),
          onTap: onTap,
        ),
        const Divider(height: 1, thickness: 0.5, indent: 16, endIndent: 16),
      ],
    );
  }
}
