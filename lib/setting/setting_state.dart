
enum SettingEventType {
  lookPrivacy,
  lookService,
  clearCache,
  forgetPsd,
  logout,
  cancelAccount,
}

class SettingState {
  String? version;
  String? cacheSize;
  bool isCancelAccountDialogVisible = false;
  bool isCancelingAccount = false;
  String? cancelAccountError;
  
  SettingState init() {
    return SettingState()
    ..version = null
    ..cacheSize = null
    ..isCancelAccountDialogVisible = false
    ..isCancelingAccount = false
    ..cancelAccountError = null;
  }

  SettingState clone() {
    return SettingState()
    ..version = version
    ..cacheSize = cacheSize
    ..isCancelAccountDialogVisible = isCancelAccountDialogVisible
    ..isCancelingAccount = isCancelingAccount
    ..cancelAccountError = cancelAccountError;
  }
}
