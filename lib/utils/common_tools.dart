import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/amap_location_utils.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:crypto/crypto.dart';
import 'package:dart_sm/dart_sm.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:fluwx/fluwx.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:steel_crypt/steel_crypt.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

import 'AppApplication.dart';
import 'StrUtil.dart';
import 'appTheme.dart';

class CommonTools {
  /// s4 数据加密
  static String wjEncrypt(dynamic data,String sm4KeyString ) {
    String jsonString = "";
    if (data == null){
      jsonString = jsonEncode("");
    }else{
      jsonString = jsonEncode(data);
    }

    String sm4String = SM4.encrypt(jsonString, key: sm4KeyString);
    // String sm2String = SM2.encrypt(sm4String, G.sm2PublicKey, cipherMode: 1);
    return sm4String;
  }

  /// 加密sm4公钥
  static String encryptSM4Key(String sm4String){
    String sm4EncryptString = SM2.encrypt(sm4String, StrUtil.sm2PublicKey, cipherMode: 1);
    return "04$sm4EncryptString";
  }

  /// 获取sm4公钥
  static String getPublicKey() {
    final random = Random();
    // 生成16个随机字节
    final bytes = List.generate(16, (index) => random.nextInt(256));
    // 将字节列表转换为十六进制字符串
    return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join();
  }

  /// sm2解密
  static dynamic wjDecrypt(dynamic data,) {
    String sm4String = SM2.decrypt(data, StrUtil.sm2PrivateKey);
    return jsonDecode(sm4String);
  }

  /// 获取当前项目的公钥还是私钥
  static void getProjectEncryptSetting(){
    if (Platform.isIOS){
      StrUtil.clientId = StrUtil.iosClientId;
      StrUtil.sm2PublicKey = StrUtil.iosRequestPublic;
      StrUtil.sm2PrivateKey = StrUtil.iosResponsePrivate;
      StrUtil.secret = StrUtil.iosSecret;
    }else if(Platform.isAndroid){
      StrUtil.clientId = StrUtil.androidClientId;
      StrUtil.secret = StrUtil.androidSecret;
      StrUtil.sm2PublicKey = StrUtil.androidRequestPublic;
      StrUtil.sm2PrivateKey = StrUtil.androidResponsePrivate;
    }
    logger.d("StrUtil.clientId: ${StrUtil.clientId} \n");
    logger.d("StrUtil.secret: ${StrUtil.secret} \n");
    logger.d("StrUtil.sm2PublicKey: ${StrUtil.sm2PublicKey} \n");
    logger.d("StrUtil.sm2PrivateKey: ${StrUtil.sm2PrivateKey} \n");
  }


  //密码加密
  static String generateMd5(String data) {
    var content = Utf8Encoder().convert(data);
    var digest = md5.convert(content);
    return digest.toString();
  }

  /// version1:远程版本
  /// version2:本地版本
// 代码版本比较
  static int compareAppVersions(String version1, String version2) {
    List<int> version1Components = version1.split('.').map(int.parse).toList();
    List<int> version2Components = version2.split('.').map(int.parse).toList();

    int maxLength = version1Components.length > version2Components.length
        ? version1Components.length
        : version2Components.length;

    for (int i = 0; i < maxLength; i++) {
      int value1 = (i < version1Components.length) ? version1Components[i] : 0;
      int value2 = (i < version2Components.length) ? version2Components[i] : 0;

      if (value1 < value2) return -1;
      if (value1 > value2) return 1;
    }
    return 0;
  }

  // 校验身份证合法性
  static bool verifyCardId(String cardId) {
    const Map city = {
      11: "北京",
      12: "天津",
      13: "河北",
      14: "山西",
      15: "内蒙古",
      21: "辽宁",
      22: "吉林",
      23: "黑龙江 ",
      31: "上海",
      32: "江苏",
      33: "浙江",
      34: "安徽",
      35: "福建",
      36: "江西",
      37: "山东",
      41: "河南",
      42: "湖北 ",
      43: "湖南",
      44: "广东",
      45: "广西",
      46: "海南",
      50: "重庆",
      51: "四川",
      52: "贵州",
      53: "云南",
      54: "西藏 ",
      61: "陕西",
      62: "甘肃",
      63: "青海",
      64: "宁夏",
      65: "新疆",
      71: "台湾",
      81: "香港",
      82: "澳门",
      91: "国外 "
    };
    String tip = '';
    bool pass = true;

    RegExp cardReg = RegExp(
        r'^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$');
    if (cardId.isEmpty || !cardReg.hasMatch(cardId)) {
      tip = '身份证号格式错误';
      logger.d(tip);
      pass = false;
      return pass;
    }
    if (city[int.parse(cardId.substring(0, 2))] == null) {
      tip = '地址编码错误';
      logger.d(tip);
      pass = false;
      return pass;
    }
    // 18位身份证需要验证最后一位校验位，15位不检测了，现在也没15位的了
    if (cardId.length == 18) {
      List numList = cardId.split('');
      //∑(ai×Wi)(mod 11)
      //加权因子
      List factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      //校验位
      List parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
      int sum = 0;
      int ai = 0;
      int wi = 0;
      for (var i = 0; i < 17; i++) {
        ai = int.parse(numList[i]);
        wi = factor[i];
        sum += ai * wi;
      }
      var last = parity[sum % 11];
      if (parity[sum % 11].toString() != numList[17]) {
        tip = "校验位错误";
        logger.d(tip);
        pass = false;
      }
    } else {
      tip = '身份证号不是18位';
      logger.d(tip);
      pass = false;
    }
//  wjPrint('证件格式$pass');
    return pass;
  }

  // 根据身份证号获取年龄
  static String getBirthDayFromCardId(String cardId) {
    bool isRight = verifyCardId(cardId);
    if (!isRight) {
      return "";
    }
    int len = cardId.length;
    String strBirthday = "";
    if (len == 18) {
      //处理18位的身份证号码从号码中得到生日和性别代码
      strBirthday = "${cardId.substring(6, 10)}-${cardId.substring(10, 12)}-${cardId.substring(12, 14)}";
    }
    if (len == 15) {
      strBirthday = "19${cardId.substring(6, 8)}-${cardId.substring(8, 10)}-${cardId.substring(10, 12)}";
    }
    return strBirthday;
  }

// 根据出生日期获取年龄
  static int getAgeFromBirthday(String strBirthday) {
    if (strBirthday.isEmpty) {
      logger.d('生日错误');
      return 0;
    }
    DateTime birth = DateTime.parse(strBirthday);
    DateTime now = DateTime.now();

    int age = now.year - birth.year;
    //再考虑月、天的因素
    if (now.month < birth.month ||
        (now.month == birth.month && now.day <= birth.day)) {
      age--;
    }
    return age;
  }

  // 根据身份证获取性别
  static String getSexFromCardId(String cardId) {
    String sex = "";
    bool isRight = verifyCardId(cardId);
    if (!isRight) {
      return sex;
    }
    if (cardId.length == 18) {
      if (int.parse(cardId.substring(16, 17)) % 2 == 1) {
        sex = "男";
      } else {
        sex = "女";
      }
    }
    if (cardId.length == 15) {
      if (int.parse(cardId.substring(14, 15)) % 2 == 1) {
        sex = "男";
      } else {
        sex = "女";
      }
    }
    return sex;
  }

  // 密码校验
  static bool checkPassWord(String pas) {
    int i = 0;
    RegExp regExp1 = RegExp('[0-9]');
    RegExp regExp2 = RegExp('[A-Z]');
    RegExp regExp3 = RegExp('[a-z]');
    RegExp regExp4 = RegExp('[\x21-\x2f]|[\x3a-\x40]|[\x5b-\x60]|[\x7b-\x7e]');

    if (regExp1.hasMatch(pas)) i++;
    if (regExp2.hasMatch(pas)) i++;
    if (regExp3.hasMatch(pas)) i++;
    if (regExp4.hasMatch(pas)) i++;
    if (i < 3) {
      return false;
    } else {
      return true;
    }
  }

  // 自定的Dialog提示
  static void showCustomToast(
      {required BuildContext context, required String titleText, required String subTitleText, required int time}) {
    if (Platform.isAndroid) {
      final overlay = Overlay.of(context);
      OverlayEntry overlayEntry;

      overlayEntry = OverlayEntry(builder: (context) {
        return Material(
          color: Colors.transparent,
          child: Container(
            alignment: Alignment.topCenter,
            margin: EdgeInsets.only(top: 30),
            width: MediaQuery.of(context).size.width,
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              decoration: BoxDecoration(
                color: AppTheme.nearlyWhite,
                borderRadius: BorderRadius.circular(10.0),
              ),
              margin: EdgeInsets.symmetric(horizontal: 10.0),
              padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    titleText ?? '',
                    style: TextStyle(
                        color: AppTheme.textBlack,
                        fontSize: 18,
                        fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Text(
                    subTitleText ?? '',
                    style: TextStyle(color: AppTheme.textBlack, fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
        );
      });

      overlay.insert(overlayEntry);

      Future.delayed(Duration(seconds: time ?? 3), () {
        overlayEntry.remove();
      });
    }
  }

  /// 隐私权限弹框
  void requestPrivacy() {
    Future.delayed(Duration.zero, () async {

      bool isOne = SpUtil.instance.getBool(StrUtil.isAgree);
      logger.d("------------$isOne");
      if (isOne) {
        showDeleteConfirmDialog();
      } else {
        initThirdParty();
      }
    });
  }

  initThirdParty() async {
    AmapLocationUtils.init();
    Fluwx().registerApi(appId: "wx01962cc4ae161f91",universalLink: "https://0oyh2i.xinstall.com.cn/tolink/");
    String channelString = "";
    if (Platform.isAndroid) {
      channelString = "青桐智盒/android";
    } else if (Platform.isIOS) {
      channelString = "青桐智盒/ios";
    }
    UmengCommonSdk.initCommon("667a7a43cac2a664de54b001",
        "667a7a7c940d5a4c4976eafd", channelString);
    UmengCommonSdk.setPageCollectionModeAuto();
    // if (AppApplication.getInstance().isGranted == 0) {
    //   CommonTools.requestForegroundService();
    // }
  }

// 弹出对话框
  Future<bool?> showDeleteConfirmDialog() {
    return showDialog<bool>(
      barrierDismissible: false,
      context: AppApplication.currentContext!,
      builder: (context) {
        return PopScope(
          canPop: false,
          child: AlertDialog(
            title: const Text("隐私政策"),
            content: RichText(
              text: TextSpan(
                text: '欢迎你使用青桐智盒APP，请你仔细阅读并充分理解 ',
                style: TextStyle(color: Colors.black, fontSize: 18.0),
                children: <TextSpan>[
                  TextSpan(
                    text: '《隐私政策》 ',
                    style: TextStyle(
                      color: AppTheme.themeBlue,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () async {
                        AppApplication.getCurrentState()?.pushNamed(RoutePaths.webViewWidget,arguments: {
                          "url": StrUtil.privacyPolicy,
                          "title": "隐私政策",
                        });
                      },
                  ),
                  TextSpan(text: '如你同意'),
                  TextSpan(
                    text: '《隐私政策》 ',
                    style: TextStyle(
                      color: AppTheme.themeBlue,
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () async {
                        AppApplication.getCurrentState()?.pushNamed(RoutePaths.webViewWidget,arguments: {
                          "url": StrUtil.privacyPolicy,
                          "title": "隐私政策",
                        });
                      },
                  ),
                  TextSpan(
                    text: '的全部内容，请点击“同意”开始使用我们的服务 ',
                  ),
                ],
              ),
            ),
            actions: <Widget>[
              MaterialButton(
                child: Text("取消"),
                onPressed: () => AppApplication.getCurrentState()?.pop(),
              ),
              MaterialButton(
                child: Text("同意"),
                onPressed: () async {
                  //关闭对话框并返回true
                  SpUtil.instance.setBool(StrUtil.isAgree, false);
                  Navigator.of(context).pop(true);
                  await initThirdParty() ;
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 弹出权限对话框
  static Future showPermissionDialog(
      {String str = "", Function? cancelCallBack, Function? settingCallBack}) {
    return showDialog(
      barrierDismissible: false,
      context: AppApplication.currentContext!,
      builder: (context) {
        return AlertDialog(
          title: Text("权限提示"),
          content: Text(
            "使用我们服务，需要你允许相关权限${str.isEmpty ? "" : "($str)"}，请到手机权限设置中设置",
            style: TextStyle(color: Colors.black, fontSize: 18.0),
          ),
          actions: <Widget>[
            // ignore: deprecated_member_use
            TextButton(
              child: Text("拒绝"),
              onPressed: () {
                Navigator.of(context).pop(true);
                cancelCallBack?.call();
              },
            ),
            // ignore: deprecated_member_use
            TextButton(
              child: Text("确认"),
              onPressed: () async {
                //关闭对话框并返回true
                Navigator.of(context).pop(true);
                openAppSettings();
                settingCallBack?.call();
              },
            ),
          ],
        );
      },
    );
  }

  /// 上传定位信息或ip地址信息
 static uploadLocationOrIpAddress(map, {Function? errorCallBack,Function? resultCallBack}) async{
    final response = await ApiInstance().post("${StrUtil.notaryModule}/cgz/geo/updBusiGeoInfo",data: map,errorFunction: (){
      errorCallBack?.call();
    });
    if(response != null && response["code"] == 200){
     resultCallBack?.call();
    }
 }

  /// 获取ip信息
  static Future<String?> getDeviceIPAddress() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var wifiInfo = await NetworkInfo().getWifiIP();
      logger.d("打印获取的ip地址：$wifiInfo");
      return wifiInfo;
    }
    return "";
  }
}