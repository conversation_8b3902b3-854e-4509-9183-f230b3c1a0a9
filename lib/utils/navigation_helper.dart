import 'package:flutter/material.dart';
import 'package:bloc_test/utils/page_transitions.dart';
import 'package:bloc_test/utils/AppApplication.dart';

/// 导航辅助类，提供各种转场动画的导航方法
class NavigationHelper {
  
  /// 使用淡入淡出动画导航
  static Future<T?> fadeNavigateTo<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: PageTransitionType.fade,
        duration: duration,
      ) as Route<T>,
    );
  }
  
  /// 使用滑动动画导航
  static Future<T?> slideNavigateTo<T extends Object?>(
    Widget page, {
    PageTransitionType slideType = PageTransitionType.slideLeft,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: slideType,
        duration: duration,
        curve: curve,
      ) as Route<T>,
    );
  }
  
  /// 使用缩放动画导航
  static Future<T?> scaleNavigateTo<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.elasticOut,
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: PageTransitionType.scale,
        duration: duration,
        curve: curve,
      ) as Route<T>,
    );
  }
  
  /// 使用旋转动画导航
  static Future<T?> rotateNavigateTo<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 500),
    Curve curve = Curves.easeInOut,
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: PageTransitionType.rotation,
        duration: duration,
        curve: curve,
      ) as Route<T>,
    );
  }
  
  /// 使用iOS风格动画导航
  static Future<T?> cupertinoNavigateTo<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: PageTransitionType.cupertino,
        duration: duration,
        curve: Curves.linearToEaseOut,
      ) as Route<T>,
    );
  }
  
  /// 使用Material风格动画导航
  static Future<T?> materialNavigateTo<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: PageTransitionType.material,
        duration: duration,
      ) as Route<T>,
    );
  }
  
  /// 使用自定义动画导航
  static Future<T?> customNavigateTo<T extends Object?>(
    Widget page, {
    required PageTransitionType transitionType,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: transitionType,
        duration: duration,
        curve: curve,
      ) as Route<T>,
    );
  }
  
  /// 使用共享轴动画导航
  static Future<T?> sharedAxisNavigateTo<T extends Object?>(
    Widget page, {
    Duration duration = const Duration(milliseconds: 300),
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).push<T>(
      PageTransitions.createSharedAxisRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        duration: duration,
      ) as Route<T>,
    );
  }
  
  /// 安全的返回导航（带动画）
  static void safePopWithAnimation<T extends Object?>([T? result]) {
    final context = AppApplication.currentContext;
    if (context != null && Navigator.of(context).canPop()) {
      Navigator.of(context).pop<T>(result);
    }
  }
  
  /// 替换当前页面（带动画）
  static Future<T?> replaceWithAnimation<T extends Object?, TO extends Object?>(
    Widget page, {
    PageTransitionType transitionType = PageTransitionType.fade,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    TO? result,
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).pushReplacement<T, TO>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: transitionType,
        duration: duration,
        curve: curve,
      ) as Route<T>,
      result: result,
    );
  }
  
  /// 清除所有页面并导航到新页面
  static Future<T?> pushAndRemoveUntilWithAnimation<T extends Object?>(
    Widget page, {
    PageTransitionType transitionType = PageTransitionType.fade,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
    bool Function(Route<dynamic>)? predicate,
  }) {
    final context = AppApplication.currentContext;
    if (context == null) return Future.value(null);
    
    return Navigator.of(context).pushAndRemoveUntil<T>(
      PageTransitions.createRoute(
        page: page,
        settings: RouteSettings(name: page.runtimeType.toString()),
        type: transitionType,
        duration: duration,
        curve: curve,
      ) as Route<T>,
      predicate ?? (route) => false,
    );
  }
}
