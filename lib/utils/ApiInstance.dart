import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/requestInterceptor.dart';
import 'package:dio/dio.dart';

class ApiInstance {
  static final ApiInstance _singleton = ApiInstance._internal();

  factory ApiInstance() {
    return _singleton;
  }

  late Dio dio;

  ApiInstance._internal() {
    // 初始化 Dio 实例
    dio = Dio(
      BaseOptions(
        baseUrl: StrUtil.hostUrl, // 替换为你的基础 URL
        connectTimeout: const Duration(seconds: 60),
        receiveTimeout: const Duration(seconds: 60),
        headers: {},
        contentType: Headers.jsonContentType,
        responseType: ResponseType.plain,
      ),

    );

    // 可添加拦截器、日志等
    dio.interceptors.add(RequestInterceptor());
  }

  // GET 请求示例
  Future<dynamic> get(String url, {Map<String, dynamic>? queryParameters,Function? errorFunction, Options? options}) async {
    try {
      final response = await dio.get(url, queryParameters: queryParameters,options: options);
      return response.data;
    } catch (e) {
      errorFunction?.call(e);
      rethrow;
    }
  }

  // POST 请求示例
  Future<dynamic> post(String url, {dynamic data, Map<String, dynamic>? queryParameters,Options? options,Function? errorFunction}) async {
    try {
      final response = await dio.post(url, data: data, queryParameters: queryParameters,options:  options);
      return response.data;
    } catch (e) {
      errorFunction?.call(e);
      rethrow;
    }
  }
}
