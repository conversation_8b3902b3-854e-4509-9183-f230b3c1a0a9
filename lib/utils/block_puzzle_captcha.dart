import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:steel_crypt/steel_crypt.dart';

import 'event_bus_instance.dart';


typedef VoidSuccessCallback = dynamic Function(String v,String token);
class BlockPuzzleCaptchaPage extends StatefulWidget {
  final VoidSuccessCallback onSuccess; //拖放完成后验证成功回调
  final VoidCallback onFail; //拖放完成后验证失败回调
  final VoidCallback close;

  const BlockPuzzleCaptchaPage({super.key, required this.onSuccess, required this.onFail, required this.close});

  @override
  _BlockPuzzleCaptchaPageState createState() => _BlockPuzzleCaptchaPageState();
}

class _BlockPuzzleCaptchaPageState extends State<BlockPuzzleCaptchaPage>
    with TickerProviderStateMixin {
  String baseImageBase64 = "";
  String slideImageBase64 = "";
  String captchaToken = "";
  String secretKey = "";//加密key

  Size baseSize = Size.zero; //底部基类图片
  Size slideSize = Size.zero; //滑块图片


  var sliderColor = Colors.white; //滑块的背景色
  var sliderIcon = Icons.arrow_forward; //滑块的图标
  var movedXBorderColor = Colors.white; //滑块拖动时，左边已滑的区域边框颜色
  double sliderStartX = 0; //滑块未拖前的X坐标
  double sliderXMoved = 0;
  bool sliderMoveFinish = false; //滑块拖动结束
  bool checkResultAfterDrag = false; //拖动后的校验结果

  //-------------动画------------
  int _checkMilliseconds = 0; //滑动时间
  bool _showTimeLine = false; //是否显示动画部件
  bool _checkSuccess = false; //校验是否成功
  late AnimationController controller;

  //高度动画
  late Animation<double> offsetAnimation;

  //底部部件key
  final GlobalKey _containerKey = GlobalKey();
  //背景图key
  final GlobalKey _baseImageKey = GlobalKey();
  //滑块
  final GlobalKey _slideImageKey = GlobalKey();
  final double _bottomSliderSize = 60;

  late StreamSubscription listenStream;


  //------------动画------------

  // //校验通过
  // void checkSuccess(String content) {
  //   setState(() {
  //     checkResultAfterDrag = true;
  //     _checkSuccess = true;
  //     _showTimeLine = true;
  //   });
  //   _forwardAnimation();
  //   updateSliderColorIcon();
  //
  //   //刷新验证码
  //   Future.delayed(Duration(milliseconds: 1000)).then((v) {
  //     _reverseAnimation().then((v) {
  //       setState(() {
  //         _showTimeLine = false;
  //       });
  //       //回调
  //       if (widget.onSuccess != null) {
  //         widget.onSuccess(content);
  //       }
  //       //关闭验证码
  //       print(content);
  //       Navigator.pop(context);
  //     });
  //   });
  // }

  //校验失败
  void checkFail() {
    setState(() {
      _showTimeLine = true;
      _checkSuccess = false;
      checkResultAfterDrag = false;
    });
    _forwardAnimation();
    updateSliderColorIcon();

    //刷新验证码
    Future.delayed(Duration(milliseconds: 1000)).then((v) {
      _reverseAnimation().then((v) {
        setState(() {
          _showTimeLine = false;
        });
        loadCaptcha();
        //回调
        widget.onFail();
            });
    });
  }

  //重设滑动颜色与图标
  void updateSliderColorIcon() {
    Color sliderColor; //滑块的背景色
    IconData sliderIcon; //滑块的图标
    Color movedXBorderColor; //滑块拖动时，左边已滑的区域边框颜色

    //滑块的背景色
    if (sliderMoveFinish) {
      //拖动结束
      sliderColor = checkResultAfterDrag ? Colors.green : Colors.red;
      sliderIcon = checkResultAfterDrag ? Icons.check : Icons.close;
      movedXBorderColor = checkResultAfterDrag ? Colors.green : Colors.red;
    } else {
      //拖动未开始或正在拖动中
      sliderColor = sliderXMoved > 0 ? Color(0xff447ab2) : Colors.white;
      sliderIcon = Icons.arrow_forward;
      movedXBorderColor = Color(0xff447ab2);
    }

    sliderColor = sliderColor;
    sliderIcon = sliderIcon;
    movedXBorderColor = movedXBorderColor;
    setState(() {});
  }

  //加载验证码
  void loadCaptcha() {
    setState(() {
      _showTimeLine = false;
      sliderMoveFinish = false;
      checkResultAfterDrag = false;
      sliderColor = Colors.white; //滑块的背景色
      sliderIcon = Icons.arrow_forward; //滑块的图标
      movedXBorderColor = Colors.white; //滑块拖动时，左边已滑的区域边框颜色
    });
    ApiInstance().get("${StrUtil.userModule}/cgz/userApp/generateCaptchaGif",queryParameters: {},errorFunction: (error){
      ToastUtil.showErrorToast("网络错误，请稍后再试！");
      widget.onFail();
    }).then((value) async {
      if(value != null && value['code'] ==200) {
        Map<String, dynamic> repData = value['data'];
        sliderXMoved = 0;
        sliderStartX = 0;
        captchaToken = '';
        checkResultAfterDrag = false;

        baseImageBase64 = repData["originalImageBase64"];
        secretKey = repData["st"] ?? "";
        baseImageBase64 = baseImageBase64.replaceAll('\n', '');
        slideImageBase64 = repData["jigsawImageBase64"];
        slideImageBase64 = slideImageBase64.replaceAll('\n', '');
        captchaToken = repData["tn"];

        var baseR = await WidgetUtil.getImageWH(
            image: Image.memory(Base64Decoder().convert(baseImageBase64)));
        baseSize = baseR.size;

        var silderR = await WidgetUtil.getImageWH(
            image: Image.memory(Base64Decoder().convert(slideImageBase64)));
        slideSize = silderR.size;
        setState(() {

        });
      } else  {
        widget.onFail();
        ToastUtil.showErrorToast(value['msg']??value['message']??value['data']??'网络出错了，请稍后再试！');
      }
    });
  }

  //校验验证码
  void checkCaptcha(sliderXMoved, captchaToken) {
    setState(() {
      sliderMoveFinish = true;
    });
    //滑动结束，改变滑块的图标及颜色
//    updateSliderColorIcon();

    //pointJson参数需要aes加密

//    MediaQueryData mediaQuery = MediaQuery.of(myContext);
    var pointMap = {"x": sliderXMoved, "y": 5};
    var pointStr = json.encode(pointMap);
    var cryptedStr = pointStr;
    // secretKey 不为空 进行as加密
    if(secretKey.isNotEmpty){

      cryptedStr = aesEncode(key: secretKey, content: pointStr);
      var dcrypt = aesDecode(key: secretKey, content: cryptedStr);
      json.decode(dcrypt);
      
    }
    widget.onSuccess(toRadixString(cryptedStr),captchaToken);
  }

  ///aes加密
  /// [key]AesCrypt加密key
  /// [content] 需要加密的内容字符串
  String aesEncode({required String key, required String content}) {
    var aesCrypt = AesCrypt(
        key: base64UrlEncode(key.codeUnits), padding: PaddingAES.pkcs7);
    return aesCrypt.ecb.encrypt(inp: content);
  }

  ///aes解密
  /// [key]aes解密key
  /// [content] 需要加密的内容字符串
  String aesDecode({required String key, required String content}) {
    var aesCrypt = AesCrypt(
        key: base64UrlEncode(key.codeUnits), padding: PaddingAES.pkcs7);
    return aesCrypt.ecb.decrypt(enc: content);
  }
  
  String toRadixString(String value){
    var res = '';
    for (int i = 0; i < value.length; i++){
      res=res+value.codeUnitAt(i).toRadixString(16);
    }
    return res;
  }

  @override
  void initState() {
    super.initState();
    initAnimation();
    loadCaptcha();
    // EventBusInstanceEvent(source: 'checkFail'),
    listenStream = eventBus.on<EventBusInstanceEvent>().listen((event) {
      if(event.source == 'checkFail') {
        checkFail();
      }
    });
  }

  @override
  void dispose() {
    controller.dispose();
    listenStream.cancel();
    super.dispose();
  }

  // 初始化动画
  void initAnimation() {
    controller =
        AnimationController(duration: Duration(milliseconds: 500), vsync: this);

    offsetAnimation = Tween<double>(begin: 0.5, end: 0)
        .animate(CurvedAnimation(parent: controller, curve: Curves.ease))
          ..addListener(() {
            setState(() {});
          });
  }

  // 反向执行动画
  _reverseAnimation() async {
    await controller.reverse();
  }

  // 正向执行动画
  _forwardAnimation() async {
    await controller.forward();
  }

  @override
  void didUpdateWidget(BlockPuzzleCaptchaPage oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return MaxScaleTextWidget(
      child: buildContent(context),
    );
  }

  Widget buildContent(BuildContext context) {
    var mediaQuery = MediaQuery.of(context);
    var dialogWidth = 0.9 * mediaQuery.size.width;
    if (dialogWidth < 330) {
      dialogWidth = mediaQuery.size.width;
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Center(
        child: Container(
          key: _containerKey,
          width: dialogWidth,
          height: 340,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(16.r)),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              _topContainer(),
              _middleContainer(),
              _bottomContainer(),
            ],
          ),
        ),
      ),
    );
  }

  ///顶部，提示+关闭
  _topContainer() {
    return Container(
      height: 50,
      padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(width: 1, color: Color(0xffe5e5e5))),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            '请完成安全验证',
            style: TextStyle(fontSize: 18),
          ),
          IconButton(
              icon: Icon(Icons.highlight_off),
              iconSize: 30,
              color: Colors.black38,
              onPressed: () {
                //退出
                widget.close();
                Navigator.pop(context);
              }),
        ],
      ),
    );
  }

  _middleContainer() {
    ////显示验证码
    return Container(
      margin: EdgeInsets.symmetric(vertical: 10),
      child: Stack(
        children: <Widget>[
          ///底图 310*155
          baseImageBase64.isNotEmpty
              ? Image.memory(
            Base64Decoder().convert(baseImageBase64),
            fit: BoxFit.fitWidth,
            key: _baseImageKey,
            gaplessPlayback: true,
          )
              : Container(
            width: 310,
            height: 155,
            alignment: Alignment.center,
            child: CircularProgressIndicator(),
          ),

          ///滑块图
          slideImageBase64.isNotEmpty
              ? Container(
            margin: EdgeInsets.fromLTRB(sliderXMoved, 0, 0, 0),
            child: Image.memory(
              Base64Decoder().convert(slideImageBase64),
              fit: BoxFit.fitHeight,
              key: _slideImageKey,
              gaplessPlayback: true,
            ),
          )
              : Container(),

          //刷新按钮
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
                icon: Icon(Icons.refresh),
                iconSize: 30,
                color: Colors.black54,
                onPressed: () {
                  //刷新
                  loadCaptcha();
                }),
          ),
          Positioned(
              bottom: 0,
              left: -10,
              right: -10,
              child: Offstage(
                offstage: !_showTimeLine,
                child: FractionalTranslation(
                  translation: Offset(0, offsetAnimation.value),
                  child: Container(
                    margin: EdgeInsets.only(left: 10, right: 10),
                    height: 40,
                    color: _checkSuccess
                        ? Color(0x7F66BB6A)
                        : Color.fromRGBO(200, 100, 100, 0.4),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      _checkSuccess
                          ? "${(_checkMilliseconds / (60.0 * 12)).toStringAsFixed(2)}s验证成功"
                          : "验证失败",
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              )),
          Positioned(
              bottom: -20,
              left: 0,
              right: 0,
              child: Offstage(
                offstage: !_showTimeLine,
                child: Container(
                  margin: EdgeInsets.only(left: 10, right: 10),
                  height: 20,
                  color: Colors.white,
                ),
              ))
        ],
      ),
    );
  }
  ///底部，滑动区域
  _bottomContainer() {
    return baseSize.width >0
        ? SizedBox(
        height: 70,
        width: baseSize.width,
//            color: Colors.cyanAccent,
        child: Stack(
          alignment: AlignmentDirectional.centerStart,
          children: <Widget>[
            Container(
              height: _bottomSliderSize,
              decoration: BoxDecoration(
                border: Border.all(
                  width: 1,
                  color: Color(0xffe5e5e5),
                ),
                color: Color(0xfff8f9fb),
              ),
            ),
            Container(
              alignment: Alignment.center,
              child: Text(
                '向右拖动滑块填充拼图',
                style: TextStyle(fontSize: 16),
              ),
            ),
            Container(
              width: sliderXMoved,
              height: _bottomSliderSize-2,
              decoration: BoxDecoration(
                border: Border.all(
                  width: sliderXMoved > 0 ? 1 : 0,
                  color: movedXBorderColor,
                ),
                color: Color(0xfff3fef1),
              ),
            ),
            GestureDetector(
              onPanStart: (startDetails) {///开始
                _checkMilliseconds = DateTime.now().millisecondsSinceEpoch;
                print(startDetails.localPosition);
                sliderStartX = startDetails.localPosition.dx;
              },
              onPanUpdate: (updateDetails) { ///更新
                print(updateDetails.localPosition);
                double w1 = _baseImageKey.currentContext!.size!.width - _slideImageKey.currentContext!.size!.width;
                double offset = updateDetails.localPosition.dx - sliderStartX;
                if(offset < 0){
                  offset = 0;
                }
                if(offset > w1){
                  offset = w1;
                }
                print("offset ------ $offset");
                setState(() {
                  sliderXMoved = offset;
                });
                //滑动过程，改变滑块左边框颜色
                updateSliderColorIcon();
              },
              onPanEnd: (endDetails) { //结束
                print("endDetails");
                checkCaptcha(sliderXMoved, captchaToken);
                int nowTime = DateTime.now().millisecondsSinceEpoch;
                _checkMilliseconds = nowTime - _checkMilliseconds;
              },
              child: Container(
                width: _bottomSliderSize,
                height: _bottomSliderSize,
                margin: EdgeInsets.only(left: sliderXMoved > 0 ? sliderXMoved : 1),
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      width: 1,
                      color: Color(0xffe5e5e5),
                    ),
                    right: BorderSide(
                      width: 1,
                      color: Color(0xffe5e5e5),
                    ),
                    bottom: BorderSide(
                      width: 1,
                      color: Color(0xffe5e5e5),
                    ),
                  ),
                  color: sliderColor,
                ),
                child: IconButton(
                  icon: Icon(sliderIcon),
                  iconSize: 30,
                  color: Colors.black54, onPressed: () {  },
                ),
              ),
            )
          ],
        ))
        : Container();
  }
}


class MaxScaleTextWidget extends StatelessWidget {
  final double max;
  final Widget child;

  const MaxScaleTextWidget({super.key, this.max = 1.0, required this.child});

  @override
  Widget build(BuildContext context) {
    var data = MediaQuery.of(context);
    var textScaleFactor = min(max, data.textScaleFactor);
    return MediaQuery(data: data.copyWith(textScaler: TextScaler.linear(textScaleFactor)), child: child);
  }
}
