import 'dart:convert';
import 'dart:io';

import 'package:bloc_test/models/userInfo.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'common_tools.dart';
import 'mqtt_client.dart';

class RequestInterceptor extends  InterceptorsWrapper {

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) async{
    logger.d("加密前的数据url-----${options.uri} \n requestType-----------${options.method} \n data------------${options.data} \n queryParameters----------${options.queryParameters}");
   UserInfoEntity userInfoEntity = AppApplication.getInstance().userInfoEntity;
   PackageInfo packageInfo = await PackageInfo.fromPlatform();
   if (userInfoEntity.token != null) {
     options.headers["token"] = userInfoEntity.token;
   }
   options.headers["version"] = packageInfo.version;
   options.headers['packageName'] = packageInfo.packageName;
   options.headers["clientId"] = StrUtil.clientId;
   options.headers['secret'] =  CommonTools.encryptSM4Key(StrUtil.secret);
   if (Platform.isIOS) {
     options.headers["source"] = "app-qtzh-ios";
   } else if (Platform.isAndroid) {
     options.headers["source"] = "app-qtzh-android";
   }
   logger.d("options.headers---------${options.headers}");
    String sm4PublicKey = CommonTools.getPublicKey();
   if  (options.method == "POST") {
     String sm4EncryptKey = CommonTools.encryptSM4Key(sm4PublicKey);
     options.headers['encrypt'] = sm4EncryptKey;
     if (options.data != null) {
       String encryptData;
       if (options.data is FormData) {
         logger.d("加密后的数据：直传无加密: ${options.data}");
       } else {
         encryptData = CommonTools.wjEncrypt(options.data, sm4PublicKey);
         options.data = {
           "encryptStr": encryptData,
         };
         logger.d("加密后的数据：$encryptData");
       }
     } else if (options.queryParameters.isNotEmpty) {
       String encryptData = CommonTools.wjEncrypt(options.queryParameters, sm4PublicKey);
       options.headers['content-type'] = "";
       options.queryParameters = {
         "encryptStr": encryptData,
       };
     }
   } else if (options.method == "GET") {
       options.headers['content-type'] = "";
       String sm4EncryptKey = CommonTools.encryptSM4Key(sm4PublicKey);
       options.headers['encrypt'] = sm4EncryptKey;
       String encryptData = CommonTools.wjEncrypt(options.queryParameters, sm4PublicKey);
       options.queryParameters = {
         "encryptStr": encryptData,
       };
   }
    logger.d("加密后的数据url-----${options.uri} \n requestType-----------${options.method} \n data------------${options.data} \n queryParameters----------${options.queryParameters},headers----------${options.headers}");
    super.onRequest(options, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    logger.e("网络请求报错：-------$err");
   if (err.response?.statusCode == 400) {
      logger.e("Bad request: ${err.response?.data}");
    } else if (err.response?.statusCode == 500) {
      logger.e("Server error (500): ${err.response?.data}");
      logger.e("This is likely a server-side issue. Check server logs or contact backend team.");
    } else if (err.response?.statusCode == 401) {
     if (EasyLoading.isShow) {
       EasyLoading.dismiss();
     }
     SpUtil.instance.remove(StrUtil.userInfo);
     dynamic data = err.response?.data;
     if (data is String) {
       data = json.decode(data);
       logger.d("拦截解析后的数据-----$data");
     }
     ToastUtil.showErrorToast(data["data"]);
     logger.d("当前的路由的路径名称-------${AppApplication.currentPath}");
     if (AppApplication.currentPath != RoutePaths.login) {
       AppApplication.getCurrentState()?.pushNamedAndRemoveUntil(
           RoutePaths.login, (router) => false);
     }
     MqttClientMsg.instance.disconnect();
     return;
   } else {
       logger.e("Error (${err.response?.statusCode}): ${err.response?.data}");
   }
    super.onError(err, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    logger.d("解密前的数据$response");
    dynamic data = response.data;
    if (data is String) {
      data = CommonTools.wjDecrypt(response.data);
      logger.d("拦截解析后的数据-----$data");
    }
    if (data is Map) {
      if (data['code'] == 401) {
        EasyLoading.dismiss();
        SpUtil.instance.remove(StrUtil.userInfo);
        ToastUtil.showErrorToast(data["data"]);
        if (AppApplication.currentPath != RoutePaths.login) {
          AppApplication.getCurrentState()?.pushNamedAndRemoveUntil(
              RoutePaths.login, (router) => false);
        }
        MqttClientMsg.instance.disconnect();
      }
      if (data['code'] != 200) {
        Future.error(response.data);
      }
    }
    response.data = data;
    super.onResponse(response, handler);
  }

 }