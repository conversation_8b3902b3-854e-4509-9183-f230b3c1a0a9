import 'package:bloc_test/utils/page_transitions.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:flutter/animation.dart';

/// 路由动画配置
class RouteConfig {
  
  /// 获取指定路由的转场动画类型
  static PageTransitionType getTransitionType(String routeName) {
    switch (routeName) {
      // 登录相关页面使用淡入淡出
      case RoutePaths.login:
      case RoutePaths.register:
        return PageTransitionType.fade;
        
      // 主要页面使用滑动动画
      case RoutePaths.mainIndex:
        return PageTransitionType.slideUp;
        
      // 详情页面使用从右滑入
      case RoutePaths.explain:
        return PageTransitionType.slideLeft;
        
      // 设置相关页面使用从下滑入
      case RoutePaths.settings:
      case RoutePaths.modifyPsd:
        return PageTransitionType.slideUp;
        
      // 列表页面使用Material风格

        return PageTransitionType.material;
        
      // 全屏页面使用缩放动画
      case RoutePaths.videoNotarize:

        return PageTransitionType.scale;
        






      // 默认使用滑动动画
      default:
        return PageTransitionType.slide;
    }
  }
  
  /// 获取指定路由的动画持续时间
  static Duration getTransitionDuration(String routeName) {
    switch (routeName) {
      // 快速动画
      case RoutePaths.login:
      case RoutePaths.register:
        return const Duration(milliseconds: 200);
        
      // 中等速度动画
      case RoutePaths.mainIndex:
      case RoutePaths.settings:
        return const Duration(milliseconds: 300);
        
      // 慢速动画（用于复杂页面）
      case RoutePaths.videoNotarize:
        return const Duration(milliseconds: 400);
        
      // 默认动画时长
      default:
        return const Duration(milliseconds: 250);
    }
  }
  
  /// 获取指定路由的动画曲线
  static Curve getTransitionCurve(String routeName) {
    switch (routeName) {
      // 弹性动画
      case RoutePaths.mainIndex:
        return Curves.elasticOut;
        
      // 快速进入慢速退出
      case RoutePaths.login:
      case RoutePaths.register:
        return Curves.fastOutSlowIn;
        
      // 慢速进入快速退出
      case RoutePaths.videoNotarize:
        return Curves.slowMiddle;

        
      // 默认缓动
      default:
        return Curves.easeInOut;
    }
  }
  
  /// 检查是否需要特殊处理的路由
  static bool isSpecialRoute(String routeName) {
    const specialRoutes = [
      RoutePaths.videoNotarize,
    ];
    return specialRoutes.contains(routeName);
  }
  
  /// 获取路由的返回动画类型（可以与进入动画不同）
  static PageTransitionType getExitTransitionType(String routeName) {
    switch (routeName) {
      // 某些页面退出时使用不同的动画

        
      // 大部分页面使用相同的进入和退出动画
      default:
        return getTransitionType(routeName);
    }
  }
}
