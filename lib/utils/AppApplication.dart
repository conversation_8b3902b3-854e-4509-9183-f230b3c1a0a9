
import 'package:bloc_test/models/userInfo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';


var logger = Logger(printer: LogfmtPrinter());

class AppApplication {
  static final AppApplication _singleton = AppApplication._internal();

  AppApplication._internal() {
    _userInfo = UserInfoEntity();
  }

  factory AppApplication() {
    return _singleton;
  }

  static AppApplication getInstance() {
    return _singleton;
  }

  static bool isColorFiltered = false;

  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  /// toolbar routeName
  static final List toobarRouteNameList = ['mainIndex', 'codeLogin', 'login'];
  /// 获取当前的state
  static NavigatorState? getCurrentState() => navigatorKey.currentState;

  /// 返回页面,后退
  static void pop() => getCurrentState()?.pop();

  late UserInfoEntity _userInfo;

  bool get hasUser => userInfoEntity.token != null && userInfoEntity.token!.isNotEmpty;


  UserInfoEntity get userInfoEntity {
    return _userInfo;
  }

  set userInfoEntity(UserInfoEntity userInfo) {
    _userInfo = userInfo;
  }

  // 当前的上下文
  static BuildContext? currentContext = navigatorKey.currentContext;

  //当前路由路径
  static String? currentPath;

}