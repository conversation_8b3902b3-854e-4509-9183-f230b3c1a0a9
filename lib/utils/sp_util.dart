import 'package:shared_preferences/shared_preferences.dart';

class SpUtil {
  static final SpUtil _singleton = SpUtil._internal();

  factory SpUtil() {
    return _singleton;
  }

  SpUtil._internal() {
   init();
  }

  Future init()async{
    prefs = await SharedPreferences.getInstance();
  }

  static SpUtil get instance => _singleton;

  late SharedPreferences prefs;

  void remove(String key) {
    SpUtil._singleton.prefs.remove(key);
  }

   void setString(String key, String value) {
    SpUtil._singleton.prefs.setString(key, value);
  }

   String getString(String key){
    return SpUtil._singleton.prefs.getString(key) ?? "";
  }
   void setBool(String key, bool value) {
    SpUtil._singleton.prefs.setBool(key, value);
  }
   bool getBool(String key){
    return SpUtil._singleton.prefs.getBool(key) ?? false;
  }
   void setInt(String key, int value) {
    SpUtil._singleton.prefs.setInt(key, value);
  }
   int getInt(String key){
    return SpUtil._singleton.prefs.getInt(key) ?? 0;
  }
   void setDouble(String key, double value) {
    SpUtil._singleton.prefs.setDouble(key, value);
  }
   double getDouble(String key){
    return SpUtil._singleton.prefs.getDouble(key) ?? 0.0;
  }
   void setStringList(String key, List<String> value) {
    SpUtil._singleton.prefs.setStringList(key, value);
  }
   List<String> getStringList(String key){
    return SpUtil._singleton.prefs.getStringList(key) ?? [];
  }
   void setObject(String key, Object value) {
    SpUtil._singleton.prefs.setString(key, value.toString());
  }
   Object? getObject(String key){
    return SpUtil._singleton.prefs.get(key);
  }
}