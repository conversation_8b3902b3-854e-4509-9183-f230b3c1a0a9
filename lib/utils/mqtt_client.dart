import 'dart:async';

import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';

import 'AppApplication.dart';


class MqttClientMsg {
  late MqttServerClient client;

  static MqttClientMsg get instance => _getInstance();

  static MqttClientMsg _getInstance() {
    return MqttClientMsg();
  }

  late ClientCallback clientCallback;

  void setCallback(ClientCallback clientCallback) {
    this.clientCallback = clientCallback;
  }

  Future<MqttServerClient> connect(String id) async {
    return client;
  }

  // 连接成功
  void onConnected() {
    logger.d('连接成功');
  }

  // 连接断开
  void onDisconnected() {
    logger.d('连接断开');
  }

  // 订阅主题成功
  void onSubscribed(String topic) {
    logger.d('订阅主题成功 topic: $topic');
  }

// 订阅主题失败
  void onSubscribeFail(String topic) {
    logger.d('订阅主题失败 $topic');
  }

// 成功取消订阅
  void onUnsubscribed(String? topic) {
    logger.d('成功取消订阅: $topic');
  }

// 收到 PING 响应
  void pong() {
    logger.d('收到 PING 响应');
  }

  // 开始订阅主题
  void subscribe(String topic) {
    client.subscribe(topic, MqttQos.atLeastOnce);
  }

  // 订阅主题取消
  void unsubscribe(String topic) {
    client.unsubscribe(topic);
  }

  // 断开连接
  void disconnect() {
    logger.d('断开连接: ');
    client.disconnect();
  }

  // 重新连接
  void onAutoReconnect() {
    logger.d('这是正在重新连接: ');
  }

  // 消息发布
  postMessage(String message, String topic) {
    try {
      MqttClientPayloadBuilder builder = MqttClientPayloadBuilder();
      builder.addUTF8String(message);
      client.publishMessage(topic, MqttQos.atLeastOnce, builder.payload!);
      logger.d('mqtt发送的消息:$message topic:$topic');
    } catch (e) {
      logger.d("....发送消息的错误$e");
    }
  }
}

abstract class ClientCallback {
  void clientDataHandler(onData, topic);
}
