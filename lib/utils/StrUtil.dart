import 'dart:io';

class StrUtil {

  /*
   * product 青桐智盒项目
   */
  // // 生产环境baseUrl
  // static final String hostUrl = 'https://services.njscgzc.cn:9000/';
  // // 电子公证书
  // static const String notarialCertificate =
  //     "https://sc.njguochu.com:9229/notarialCertificate";
  // // mqt 配置
  // static String mqttPath = "wss://www.scgzdt.cn/ws";
  // static int mqttPort = 15679;
  // static String mqttAccount = "gc";
  // static String mqttPassword = "Iml5imyb";
  // // ca签字
  // static String caUrl = "https://scgzdt.com:9007/";

  /*
   * test  测试环境baseUrl
   */
  // //  南京测试地址
  static final String hostUrl = 'https://testgz.njguochu.com:22203/';
  // ca签字
  static String caUrl = "https://testgz.njguochu.com:19006/";
//  电子公证书
  static const String notarialCertificate =
      "https://testgz.njguochu.com:29200/notarialCertificate";
  //
  // mqtt地址
  static String mqttPath = "wss://testgz.njguochu.com:33672/ws";
  static int mqttPort = 33672;
  static String mqttAccount = "admin";
  static String mqttPassword = "aaaaaa";


  // 华文川
  // static final String hostUrl = 'http://*************:9000/';
  // static String mqttPath = "ws://*************:15675/ws";
  // static int mqttPort = 15675;
  // static String mqttAccount = "guest";
  // static String mqttPassword = "guest";
  //



  static String userInfo = "userInfo";

  static String isAgree = "isAgree";

  /// 国密加密sm2公钥
  static String sm2PublicKey = "";
  /// 国密加密sm2私钥
  static String sm2PrivateKey = "";
  /// 国密加密sm2公钥
  static String clientId = Platform.isIOS ? iosClientId : androidClientId;
  /// 国密加密sm2私钥
  static String secret = Platform.isIOS ? iosSecret : androidSecret;

  /// 隐私政策网页
  static String privacyPolicy = "https://sc.njguochu.com:46/yszc.html";

  static String httpError = "连接服务器出错了，请稍后再试！";


  static String iosClientId = "Q0pt5zzBMlBUuaVKjDVIVuITta74TnFZ";
  static String iosSecret = "IAALfCMiohhqrBZDXSceHlyjFuThr7IG";
  static String iosRequestPublic = "0459b5c7f2c2e11b2f055b5fe1020e0252056aab35e51de6b1c230a944492a32a0bc1c0fba5dc7d0c1ee1ea0b2dc35da1fd80e79767e9502512d9f095c0badd4d1";
  static String iosResponsePrivate = "0098ae71a88daedac22ef89f4e18efeb2fe2f352354f4d4669355e10a08ce371a8";


  static String androidClientId = "EcMzMyyXv9Iz9xLnOgM4IiECU7mCkbvL";
  static String androidSecret = "OiL3colPMtTzBJ0iK6jLyUwTh2ouKynT";
  static String androidRequestPublic = "0481d53c610004a37493ce55971ae5ee292252032c73a4b250b7cdd82c4e1288507626c6531776e992562e599ad14f109a677416dc9b3cec39cbc7e0266f039120";
  static String androidResponsePrivate = "41850ede04f9f0cb6cbe9a20332b55ade59af58303327c91285d460f67a962c9";

  //腾讯RTC 的 _sdkAppId 和 _secretKey
  static int sdkAppId = **********;
  static String secretKey =
      '134d9892e8eb661c7d34f3e7476369ee9280e76f02157067c1ad5c10c6479ac5';

  //模块
  static String userModule = "/user-api";
  static String notaryModule = "/notarization";
  static String annexModule = "/annex";
  static String voteModule = "/vote";
  static String buyersModule = "/buyers-api";
  static String parkModule = "/park";
  static String identificationModule = "/identification";
  static String bankModule = "/bank";
  static String caModule = "/cgz";
  static String enforcement = '/enforcement-api';
  static String easyEnforcement = '/easy-enforcement-api';
  static String room = '/yxroom-api';
  static String appraise = "/appraise";
  static String fileSever = '/file-server';
}