enum HttpState {
  /// 初始状态
  init,

  /// 加载中
  loading,

  /// 加载完成
  loaded,

  /// 加载失败
  error,
}

extension FunctionValue on HttpState {
  String get name {
    switch (this) {
      case HttpState.init:
        return "init";
      case HttpState.loading:
        return "loading";
      case HttpState.loaded:
        return "loaded";
      case HttpState.error:
        return "error";
    }
  }
  HttpState get value {
    switch (this) {
      case HttpState.init:
        return HttpState.init;
      case HttpState.loading:
        return HttpState.loading;
      case HttpState.loaded:
        return HttpState.loaded;
      case HttpState.error:
        return HttpState.error;
    }
  }
}