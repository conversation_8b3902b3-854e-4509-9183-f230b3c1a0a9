import 'package:bloc_test/apply_information/apply_information_view.dart';
import 'package:bloc_test/confirm_information/confirm_information_view.dart';
import 'package:bloc_test/forget/forget_view.dart';
import 'package:bloc_test/models/IndustryNewsModel.dart';
import 'package:bloc_test/notary_list/notary_list_view.dart';
import 'package:bloc_test/register/register_page_view.dart';
import 'package:bloc_test/tab_bar/tab_bar_view.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/page_transitions.dart';
import 'package:bloc_test/webview_page/webview_page_view.dart';
import 'package:flutter/material.dart';
import '../binding_email_account/binding_email_account_view.dart';
import '../choose_notary_item/choose_notary_item_view.dart';
import '../code_login/code_login_view.dart';
import '../explain_page/explain_page_view.dart';
import '../guide/guide_view.dart';
import '../login_page/login_page_view.dart';
import '../animation_demo/animation_demo_page.dart';
import '../news/news_view.dart';
import '../protocol/protocol_view.dart';
import '../setting/setting_view.dart';
import '../user_service/user_service_view.dart';
import '../video_conference/video_conference_view.dart';
import '../widgets/dianzi_name.dart';
import '../widgets/guide_detail_page.dart';
import '../widgets/rule_detail.dart';
import '../setting/cancel_account_page.dart';

class RoutePaths {
  static const String login = 'login';
  static const String mainIndex = 'mainIndex';
  static const String chooseNotaryItem = 'chooseNotaryItem';
  static const String settings = 'settings';
  static const String modifyPsd = 'modifyPsd';
  static const String codeLogin = 'codeLogin';
  static const String explain = 'explain';
  static const String videoNotarize = 'videoNotarize';
  static const String register = 'register';
  static const String applyInformation = 'applyInformation';
  static const String video = 'video';
  static const String guide = 'guide';
  static const String news = 'news';
  static const String protocol = 'Protocol';
  static const String webViewWidget = "webViewWidget";
  static const String appointment = "appointment";
  static const String videoConference = "videoConference";
  static const String animationDemo = "animationDemo";
  static const String judicialExpertise = "judicialExpertise";
  static const String notaryOffice = "notaryOffice";
  static const String guideDetailPage = "guideDetailPage";
  static const String dianZiName = "dianZiName";
  static const String ruleDetail = "ruleDetail";
  static const String confirmInformation = "confirmInformation";
  static const String userService = "userService";
  static const String bindingEmailAccount = "bindingEmailAccount";
  static const String cancelAccount = "cancelAccount";
}

class RouterPage {
  //路由列表
  static final _routes = {
    'login': (BuildContext context, {Object? args}) => LoginPageView(),
    'mainIndex': (BuildContext context, {Object? args}) => TabBarPage(),
    'chooseNotaryItem':
        (BuildContext context, {Object? args}) =>
            ChooseNotaryItemPage(isAgent: args as int),
    'settings': (BuildContext context, {Object? args}) => SettingPage(),
    'modifyPsd': (context, {Object? args}) => ForgetPage(
      title: args as String,
    ),
    'codeLogin': (BuildContext context, {Object? args}) => CodeLoginPage(),
    'explain':
        (BuildContext context, {Object? args}) =>
            ExplainPage(arguments: args as int),
    'animationDemo':
        (BuildContext context, {Object? args}) => AnimationDemoPage(),
    'register':
        (BuildContext context, {Object? args}) => RegisterPage(
          unionId: (args as Map)['unionId'],
          appleId: (args)['appleId'],
          mobile: (args)['mobile'],
          loginType: (args)['loginType'],
        ),
    'ruleDetail': (BuildContext context, {Object? args}) => RuleDetailPage(),
    'dianZiName': (BuildContext context, {Object? args}) => DianziNamePage(),
    'applyInformation':
        (BuildContext context, {Object? args}) => ApplyInformationPage(
          notarialInfo: (args as Map)['notarialName'],
          selectDan: args['selectDan'],
          selectDuo: args['selectDuo'],
          purposeName: args['purposeName'],
          isAgent: args['isAgent'],
        ),
    'guide': (BuildContext context, {Object? args}) => GuidePage(),
    'notaryOffice': (BuildContext context, {Object? args}) => NotaryListPage(),
    'news':
        (BuildContext context, {Object? args}) =>
            NewsPage(model: args as IndustryNewsModel),

    'Protocol': (BuildContext context, {Object? args}) => ProtocolPage(),
    'GuideDetailPage':
        (BuildContext context, {Object? args}) =>
            GuideDetailPage(arguments: args as int),
    'webViewWidget':
        (BuildContext context, {Object? args}) => WebViewPagePage(
          titleString: (args as Map)['title'],
          urlString: (args)['url'],
        ),
    'videoConference':
        (BuildContext context, {Object? args}) =>
            VideoConferencePage(),
    'confirmInformation':
        (BuildContext context, {Object? args}) =>
            ConfirmInformationPage(
              unitGuid: (args as Map)["orderId"],
            ),
    "dianZiName":
        (BuildContext context, {Object? args}) =>
            DianziNamePage(),
    "ruleDetail":
        (BuildContext context, {Object? args}) =>
            RuleDetailPage(),
    "userService":
        (BuildContext context, {Object? args}) =>
            UserServicePage(),
    "bindingEmailAccount":
        (BuildContext context, {Object? args}) =>
            BindingEmailAccountPage(phoneNumber: args as String,),
    'cancelAccount': (BuildContext context, {Object? args}) => CancelAccountPage(),
  };

  RouterPage._internal();

  //路由监听
  static Route<dynamic> getRoutes(RouteSettings settings) {
    String? routeName = settings.name;
    final Function? builder = RouterPage._routes[routeName] as Function?;
    logger.d("CurrentPage：$settings");
    AppApplication.currentPath = routeName!;

    if (builder == null) {
      return MaterialPageRoute(
        settings: settings,
        builder:
            (BuildContext context) => Scaffold(
              body: Center(child: Text("没有找到对应的页面：${settings.name}")),
            ),
      );
    }

    // 获取转场动画配置
    // final transitionType = RouteConfig.getTransitionType(routeName);
    // final duration = RouteConfig.getTransitionDuration(routeName);
    // final curve = RouteConfig.getTransitionCurve(routeName);

    // 创建页面
    final page = Builder(
      builder: (BuildContext context) {
        return builder(context, args: settings.arguments);
      },
    );

    // 使用自定义转场动画
    return PageTransitions.createRoute(
      page: page,
      settings: settings,
      type: PageTransitionType.scale,
      duration: Duration(milliseconds: 300),
      curve: Curves.elasticOut,
    );
  }
}
