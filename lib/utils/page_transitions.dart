import 'package:flutter/material.dart';

/// 页面转场动画类型
enum PageTransitionType {
  fade,           // 淡入淡出
  slide,          // 滑动
  slideUp,        // 从下往上滑动
  slideDown,      // 从上往下滑动
  slideLeft,      // 从右往左滑动
  slideRight,     // 从左往右滑动
  scale,          // 缩放
  rotation,       // 旋转
  slideRotate,    // 滑动+旋转
  scaleRotate,    // 缩放+旋转
  cupertino,      // iOS风格
  material,       // Material风格
}

/// 页面转场动画工具类
class PageTransitions {
  
  /// 创建自定义页面路由
  static PageRouteBuilder createRoute({
    required Widget page,
    required RouteSettings settings,
    PageTransitionType type = PageTransitionType.slide,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return _buildTransition(
          type: type,
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          child: child,
          curve: curve,
        );
      },
    );
  }

  /// 构建转场动画
  static Widget _buildTransition({
    required PageTransitionType type,
    required Animation<double> animation,
    required Animation<double> secondaryAnimation,
    required Widget child,
    required Curve curve,
  }) {
    final curvedAnimation = CurvedAnimation(parent: animation, curve: curve);
    
    switch (type) {
      case PageTransitionType.fade:
        return FadeTransition(
          opacity: curvedAnimation,
          child: child,
        );
        
      case PageTransitionType.slide:
      case PageTransitionType.slideLeft:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
        
      case PageTransitionType.slideRight:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
        
      case PageTransitionType.slideUp:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, 1.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
        
      case PageTransitionType.slideDown:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.0, -1.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: child,
        );
        
      case PageTransitionType.scale:
        return ScaleTransition(
          scale: curvedAnimation,
          child: child,
        );
        
      case PageTransitionType.rotation:
        return RotationTransition(
          turns: curvedAnimation,
          child: child,
        );
        
      case PageTransitionType.slideRotate:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(curvedAnimation),
          child: RotationTransition(
            turns: Tween<double>(
              begin: 0.1,
              end: 0.0,
            ).animate(curvedAnimation),
            child: child,
          ),
        );
        
      case PageTransitionType.scaleRotate:
        return ScaleTransition(
          scale: curvedAnimation,
          child: RotationTransition(
            turns: Tween<double>(
              begin: 0.1,
              end: 0.0,
            ).animate(curvedAnimation),
            child: child,
          ),
        );
        
      case PageTransitionType.cupertino:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.linearToEaseOut,
            reverseCurve: Curves.easeInToLinear,
          )),
          child: child,
        );
        
      case PageTransitionType.material:
        return FadeTransition(
          opacity: curvedAnimation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.0, 0.25),
              end: Offset.zero,
            ).animate(curvedAnimation),
            child: child,
          ),
        );
        
      default:
        return FadeTransition(
          opacity: curvedAnimation,
          child: child,
        );
    }
  }

  /// 创建共享元素转场动画
  static PageRouteBuilder createSharedAxisRoute({
    required Widget page,
    required RouteSettings settings,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SharedAxisTransition(
          animation: animation,
          secondaryAnimation: secondaryAnimation,
          transitionType: SharedAxisTransitionType.horizontal,
          child: child,
        );
      },
    );
  }
}

/// 共享轴转场动画
class SharedAxisTransition extends StatelessWidget {
  const SharedAxisTransition({
    Key? key,
    required this.animation,
    required this.secondaryAnimation,
    required this.transitionType,
    required this.child,
  }) : super(key: key);

  final Animation<double> animation;
  final Animation<double> secondaryAnimation;
  final SharedAxisTransitionType transitionType;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation(),
      child: FadeTransition(
        opacity: _fadeAnimation(),
        child: child,
      ),
    );
  }

  Animation<Offset> _slideAnimation() {
    switch (transitionType) {
      case SharedAxisTransitionType.horizontal:
        return Tween<Offset>(
          begin: const Offset(0.3, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeInOut,
        ));
      case SharedAxisTransitionType.vertical:
        return Tween<Offset>(
          begin: const Offset(0.0, 0.3),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Curves.easeInOut,
        ));
      case SharedAxisTransitionType.scaled:
        return Tween<Offset>(
          begin: Offset.zero,
          end: Offset.zero,
        ).animate(animation);
    }
  }

  Animation<double> _fadeAnimation() {
    return Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: animation,
      curve: const Interval(0.3, 1.0, curve: Curves.easeInOut),
    ));
  }
}

/// 共享轴转场类型
enum SharedAxisTransitionType {
  horizontal,
  vertical,
  scaled,
}
