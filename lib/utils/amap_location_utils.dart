import 'dart:io';
import 'dart:math';

import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:permission_handler/permission_handler.dart';


class AmapLocationUtils {
  AmapLocationUtils._();
  // late Map<String, Object> _locationResult;

  static final AMapFlutterLocation _flutterLocation = AMapFlutterLocation();

  static init() async {
    AMapFlutterLocation.updatePrivacyShow(true, true);

    AMapFlutterLocation.updatePrivacyAgree(true);

    // requestPermission();

    initAmap();

    ///iOS 获取native精度类型
    if (Platform.isIOS) {
      requestAccuracyAuthorization();
    }
  }

  static void initAmap() {
    AMapFlutterLocation.setApiKey(
        "fefff6e0517551622d0926b3ab414ef9", "02c307e37731dc02c57b91cc563b04bb");
    logger.d("AMapFlutterLocation:初始化开始");
  }

  static void getStart(Function(Map<dynamic, dynamic>) result,
      {Function? errorCallBack}) {
    int count = 0;
    _getStop();
    _flutterLocation.onLocationChanged().listen((event) {
      logger.d("定位结果回调：$event count------$count");
      if (event["errorCode"] == 8){
         EasyLoading.showError("网络异常");
         return;
      }
      if (event['address'] != null) {
        count = 0;
        _getStop();
        logger.d("定位结果：$event");
        result(event);
      } else if (event['errorCode'] != null) {
        count = 10;
      } else {
        count++;
      }
      if (count >= 10) {
        errorCallBack?.call(event);
        _getStop();
      }

    });

    _setLocationOption();
    _flutterLocation.startLocation();
  }

  static Map bdDecrypt(double bdLng, double bdLat) {
    double xPi = pi * 3000.0 / 180.0;
    double x = bdLng - 0.0065;
    double y = bdLat - 0.006;
    double z = sqrt(x * x + y * y) - 0.00002 * sin(y * xPi);
    double theta = atan2(y, x) - 0.000003 * cos(x * xPi);
    var ggLng = z * cos(theta);
    var ggLat = z * sin(theta);
    return {'ggLng': ggLng, 'ggLat': ggLat};
  }

  static void _getStop() {
    _flutterLocation.stopLocation();
    _flutterLocation.destroy();
  }

  ///设置定位参数
  static void _setLocationOption() {
    AMapLocationOption locationOption = AMapLocationOption();

    ///是否单次定位
    locationOption.onceLocation = false;

    ///是否需要返回逆地理信息
    locationOption.needAddress = true;

    ///逆地理信息的语言类型
    locationOption.geoLanguage = GeoLanguage.DEFAULT;

    locationOption.desiredLocationAccuracyAuthorizationMode =
        AMapLocationAccuracyAuthorizationMode.FullAccuracy;

    locationOption.fullAccuracyPurposeKey = "AMapLocationScene";

    ///设置Android端连续定位的定位间隔
    locationOption.locationInterval = 2000;

    ///设置Android端的定位模式<br>
    ///可选值：<br>
    ///<li>[AMapLocationMode.Battery_Saving]</li>
    ///<li>[AMapLocationMode.Device_Sensors]</li>
    ///<li>[AMapLocationMode.Hight_Accuracy]</li>
    locationOption.locationMode = AMapLocationMode.Hight_Accuracy;

    ///设置iOS端的定位最小更新距离<br>
    locationOption.distanceFilter = -1;

    ///设置iOS端期望的定位精度
    /// 可选值：<br>
    /// <li>[DesiredAccuracy.Best] 最高精度</li>
    /// <li>[DesiredAccuracy.BestForNavigation] 适用于导航场景的高精度 </li>
    /// <li>[DesiredAccuracy.NearestTenMeters] 10米 </li>
    /// <li>[DesiredAccuracy.Kilometer] 1000米</li>
    /// <li>[DesiredAccuracy.ThreeKilometers] 3000米</li>
    locationOption.desiredAccuracy = DesiredAccuracy.Best;

    ///设置iOS端是否允许系统暂停定位
    locationOption.pausesLocationUpdatesAutomatically = false;

    ///将定位参数设置给定位插件
    _flutterLocation.setLocationOption(locationOption);
  }

  ///获取iOS native的accuracyAuthorization类型
  static void requestAccuracyAuthorization() async {
    AMapAccuracyAuthorization currentAccuracyAuthorization =
        await _flutterLocation.getSystemAccuracyAuthorization();
    if (currentAccuracyAuthorization ==
        AMapAccuracyAuthorization.AMapAccuracyAuthorizationFullAccuracy) {
      logger.d("精确定位类型");
    } else if (currentAccuracyAuthorization ==
        AMapAccuracyAuthorization.AMapAccuracyAuthorizationReducedAccuracy) {
      logger.d("模糊定位类型");
    } else {
      logger.d("未知定位类型");
    }
  }

  /// 动态申请定位权限
  static void requestPermission() async {
    // 申请权限
    bool hasLocationPermission = await requestLocationPermission();
    if (hasLocationPermission) {
      logger.d("定位权限申请通过");
    } else {
      logger.d("定位权限申请不通过");
    }
  }

  /// 申请定位权限
  /// 授予定位权限返回true， 否则返回false
  static Future<bool> requestLocationPermission() async {
    //获取当前的权限
    var status = await Permission.location.status;
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      //未授权则发起一次申请
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }
}
