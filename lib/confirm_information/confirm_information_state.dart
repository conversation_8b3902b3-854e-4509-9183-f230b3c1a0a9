class ConfirmInformationState {

  int quantity = 1;
  String? unitGuid;
  Map<String,dynamic>? result;
  List? applyUserList;
  List? notaryItemList;

  ConfirmInformationState init() {
    return ConfirmInformationState()
    ..unitGuid = null
      ..result = null
      ..applyUserList = null
      ..notaryItemList = null
    ..quantity = 1;
  }

  ConfirmInformationState clone() {
    return ConfirmInformationState()
    ..unitGuid = unitGuid
      ..result = result
      ..applyUserList = applyUserList
      ..notaryItemList = notaryItemList
    ..quantity = quantity;
  }
}
