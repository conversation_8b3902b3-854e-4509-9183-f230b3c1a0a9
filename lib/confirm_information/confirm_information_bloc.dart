import 'package:bloc/bloc.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../utils/appTheme.dart';
import '../widgets/PDFViewerPage.dart';
import 'confirm_information_event.dart';
import 'confirm_information_state.dart';

class ConfirmInformationBloc extends Bloc<ConfirmInformationEvent, ConfirmInformationState> {
  ConfirmInformationBloc() : super(ConfirmInformationState().init()) {
    on<InitEvent>(_init);
    on<IncreaseQuantityEvent>(_increaseQuantityEvent);
    on<DecreaseQuantityEvent>(_decreaseQuantityEvent);
    on<NextStepEvent>(_nextStepEvent);
    on<LookFeeRuleEvent>(_lookFeeRuleEvent);
  }

  void _init(InitEvent event, Emitter<ConfirmInformationState> emit) async {
    emit(state.clone()..unitGuid = event.unitGuid);
    Map<String,dynamic>? tempData = await getNotarizationData();
    List? lists = tempData?["notaryItems"];
    List items = [];
    if (tempData?["order"]?["isDaiBan"] == 1) {
      tempData?["applyuser"].forEach((item) {
        if (item["principal"] == null) {
          items.add(item);
        }
      });
    }
    emit(state.clone()..result = tempData
    ..notaryItemList = lists
    ..applyUserList = items);
  }

  void _increaseQuantityEvent(IncreaseQuantityEvent event, Emitter<ConfirmInformationState> emit) {
    emit(state.clone()..quantity = state.quantity + 1);
  }

  void _decreaseQuantityEvent(DecreaseQuantityEvent event, Emitter<ConfirmInformationState> emit) {
    if (state.quantity == 1) return;
    emit(state.clone()..quantity = state.quantity - 1);

  }

  void _nextStepEvent(NextStepEvent event, Emitter<ConfirmInformationState> emit) async {
  }

  void _lookFeeRuleEvent(LookFeeRuleEvent event, Emitter<ConfirmInformationState> emit) async {
    showDialog(
      context: AppApplication.currentContext!,
      builder: (context) {
        return Container(
          margin: EdgeInsets.symmetric(
            horizontal: 30,
            vertical: 50,
          ),
          padding: EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: AppTheme.white,
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: PDFViewerPage(
            pdfUrl: "https://sc.njguochu.com:46/package/sfbz.pdf",
          ),
        );
      },
    );
  }

  /// 获取订单数据
  Future<Map<String,dynamic>?> getNotarizationData()async {
    EasyLoading.show();
   final response = await ApiInstance().post("${StrUtil.notaryModule}/cgz/notaryorder/getByOrderId",data: {
      "unitGuid": state.unitGuid
    },errorFunction: (error){
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    });
     EasyLoading.dismiss();
    if (response != null && response["code"] == 200) {
      Map<String,dynamic> data = response["item"];
      return data;
    } else {
      ToastUtil.showErrorToast(response["msg"]??response["message"]??response["data"]);
      return null;
    }
  }
}
