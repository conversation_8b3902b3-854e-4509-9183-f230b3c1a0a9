import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/widgets/PDFViewerPage.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../utils/appTheme.dart';
import 'confirm_information_bloc.dart';
import 'confirm_information_event.dart';
import 'confirm_information_state.dart';

class ConfirmInformationPage extends StatelessWidget {
  final String unitGuid;
  const ConfirmInformationPage({super.key, required this.unitGuid});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create:
          (BuildContext context) =>
              ConfirmInformationBloc()..add(InitEvent(unitGuid)),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = context.read<ConfirmInformationBloc>();

    return BlocBuilder<ConfirmInformationBloc, ConfirmInformationState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(title: Text('确认订单信息')),
          body: Column(
            children: [
              // Progress Stepper
              Image.asset('images/stepThree.png', width: double.infinity),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16.w),
                  child: Column(
                    children: [
                      // Application Information Section
                      _buildApplicationInfoSection(state, context),

                      SizedBox(height: 20.h),

                      // Notarization Fees Section
                      _buildNotarizationFeesSection(state, context),

                      SizedBox(height: 20.h),

                      // Contact Information Section
                      _buildContactInfoSection(bloc, state),

                      SizedBox(height: 20.h),

                      // Tips Section
                      _buildTipsSection(context,bloc),
                    ],
                  ),
                ),
              ),
            ],
          ),
          bottomNavigationBar: _buildBottomButton(bloc, state),
        );
      },
    );
  }

  Widget _buildApplicationInfoSection(
    ConfirmInformationState state,
    BuildContext context,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.w),
        boxShadow: [
          BoxShadow(
            color: AppTheme.colorD9D9D9.withAlpha(50),
            blurRadius: 4,
            offset: Offset(0, 10.0),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(bottom: 15.h),
            child: Text(
              "申请信息",
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16.sp),
            ),
          ),
          SizedBox(
            height: 150.h,
            width: double.infinity,
            child: _applyUserInformationSection(state, context),
          ),
        ],
      ),
    );
  }

  Widget _applyUserInformationSection(
    ConfirmInformationState state,
    BuildContext context,
  ) {
    return MediaQuery.removeViewPadding(
      context: context,
      removeTop: true,
      removeBottom: true,
      child:
          state.applyUserList != null && state.applyUserList!.isNotEmpty
              ? ListView.builder(
                itemCount: state.applyUserList?.length,
                itemBuilder: (context, index) {
                  Map<String, dynamic> item = state.applyUserList![index];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "申请人",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            item["name"] ?? '',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "性别",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            item["gender"] == "1" ? '男' : '女',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "身份证号码",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            item["idCard"] ?? '',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "手机号",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            item["mobile"] ?? '',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "出生日期",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            item["birthday"] != null &&
                                    item["birthday"].toString().length > 10
                                ? item["birthday"].toString().substring(0, 10)
                                : item["birthday"],
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      Divider(color: AppTheme.bgB),
                    ],
                  );
                },
              )
              : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "申请人",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        AppApplication().userInfoEntity.userName ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "性别",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        AppApplication().userInfoEntity.gender == 1 ? '男' : '女',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "身份证号码",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        AppApplication().userInfoEntity.idCard ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "手机号",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        AppApplication().userInfoEntity.mobile ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "出生日期",
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        AppApplication().userInfoEntity.birthday != null &&
                                AppApplication().userInfoEntity.birthday
                                        .toString()
                                        .length >
                                    10
                            ? AppApplication().userInfoEntity.birthday
                                .toString()
                                .substring(0, 10)
                            : AppApplication().userInfoEntity.birthday ?? '',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  Divider(color: AppTheme.bgB),
                ],
              ),
    );
  }

  Widget _buildNotarizationFeesSection(
    ConfirmInformationState state,
    BuildContext context,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.w),
        boxShadow: [
          BoxShadow(
            color: AppTheme.colorD9D9D9.withAlpha(50),
            blurRadius: 4,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              "公证费用",
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          MediaQuery.removeViewPadding(
            context: context,
            removeBottom: true,
            removeTop: true,
            child:
                state.notaryItemList != null && state.notaryItemList!.isNotEmpty
                    ? ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: state.notaryItemList?.length ?? 0,
                      itemBuilder: (context, index) {
                        final item = state.notaryItemList?[index];
                        return _buildFeeItem(
                          "${item["notaryItemName"]} x 1",
                          "${item["price"] ?? ''} 元",
                        );
                      },
                    )
                    : const SizedBox(),
          ),
        ],
      ),
    );
  }

  Widget _buildFeeItem(String title, String amount) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.symmetric(vertical: 12.h),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: Colors.grey[200]!, width: 1)),
      ),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(fontSize: 14.sp, color: Colors.black87),
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfoSection(
    ConfirmInformationBloc bloc,
    ConfirmInformationState state,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.w),
        boxShadow: [
          BoxShadow(
            color: AppTheme.colorD9D9D9.withAlpha(50),
            blurRadius: 4,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.all(16.w),
            child: Text(
              "联系信息",
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Row(
              children: [
                Text(
                  "领取份数",
                  style: TextStyle(fontSize: 14.sp, color: Colors.black87),
                ),
                Spacer(),
                _buildQuantitySelector(bloc, state),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Text(
              "公证书您需要一式几份? (默认1份)",
              style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
            ),
          ),
          _buildFeeItem(
            "公证书x${state.notaryItemList?.length ?? ""}",
            "¥${(state.notaryItemList?.length ?? 0) * 20.0}",
          ),
        ],
      ),
    );
  }

  Widget _buildQuantitySelector(
    ConfirmInformationBloc bloc,
    ConfirmInformationState state,
  ) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            bloc.add(DecreaseQuantityEvent());
          },
          child: Container(
            width: 32.w,
            height: 32.h,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(4.w),
            ),
            child: Icon(Icons.remove, size: 16.sp, color: Colors.grey[600]),
          ),
        ),
        Container(
          width: 40.w,
          height: 32.h,
          margin: EdgeInsets.symmetric(horizontal: 8.w),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(4.w),
          ),
          child: Center(
            child: Text(
              state.quantity.toString(),
              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500),
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            bloc.add(IncreaseQuantityEvent());
          },
          child: Container(
            width: 32.w,
            height: 32.h,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(4.w),
            ),
            child: Icon(Icons.add, size: 16.sp, color: Colors.grey[600]),
          ),
        ),
      ],
    );
  }

  Widget _buildTipsSection(BuildContext context,ConfirmInformationBloc block) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.white,
        borderRadius: BorderRadius.circular(16.w),
        boxShadow: [
          BoxShadow(
            color: AppTheme.colorD9D9D9.withAlpha(50),
            blurRadius: 4,
            offset: Offset(0, 10), // changes position of shadow
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(left: 16.w, top: 16.w, bottom: 10.w),
            child: Text(
              "提示",
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Text(
              "此收费只包含中外文公证词费用,涉及公证对象文书的翻译费用按实际发生金额另行收取。",
              style: TextStyle(fontSize: 14.sp, color: Colors.red, height: 1.4),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: GestureDetector(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return Container(
                      margin: EdgeInsets.symmetric(
                        horizontal: 30,
                        vertical: 50,
                      ),
                      padding: EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: AppTheme.white,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                      ),
                      child: PDFViewerPage(
                        pdfUrl: "https://sc.njguochu.com:46/package/sfbz.pdf",
                      ),
                    );
                  },
                );
              },
              child: Text(
                "(点击查看公证收费标准)",
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppTheme.themeBlue,
                  decoration: TextDecoration.underline,
                ),
              ),
            ),
          ),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  Widget _buildBottomButton(
    ConfirmInformationBloc bloc,
    ConfirmInformationState state,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 48.h,
          child: ElevatedButton(
            onPressed: () {
              bloc.add(NextStepEvent());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.themeBlue,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.w),
              ),
            ),
            child: Text(
              '下一步',
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ),
    );
  }
}
