/// classify : 2
/// unitGuid : "2eaa1749-2dcf-491b-9453-296d5864d033"
/// children : [{"superior":"2eaa1749-2dcf-491b-9453-296d5864d033","unitGuid":"af9ccbd5-743c-4bca-b140-721ca5d0de4d","price":200.0,"grade":2,"name":"资助出国留学协议公证"}]

class NotaryOfficeItem {
  NotaryOfficeItem({
      String? classify,
      String? unitGuid,
      List<Children>? children,
      String? grade,
      String? name}){
    _classify = classify;
    _unitGuid = unitGuid;
    _children = children;
    _grade = grade;
    _name = name;
  }

  NotaryOfficeItem.fromJson(dynamic json) {
    _classify = json['classify'];
    _unitGuid = json['unitGuid'];
    _grade = json['grade'];
    _name = json['name'];
    if (json['children'] != null) {
      _children = [];
      json['children'].forEach((v) {
        _children?.add(Children.fromJson(v));
      });
    }
  }
  String? _classify;
  String? _unitGuid;
  List<Children>? _children;
  String? _grade;
  String? _name;
NotaryOfficeItem copyWith({
String? classify,
  String? unitGuid,
  List<Children>? children,
String? grade,
String? name,
}) => NotaryOfficeItem(
classify: classify ?? _classify,
  unitGuid: unitGuid ?? _unitGuid,
  children: children ?? _children,
  grade: grade ?? _grade,
  name: name ?? _name,
);
  String? get classify => _classify;
  String? get unitGuid => _unitGuid;
  List<Children>? get children => _children;
  String? get grade => _grade;
  String? get name => _name;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['classify'] = _classify;
    map['unitGuid'] = _unitGuid;
    map["grade"] = _grade;
    map["name"] = _name;
    if (_children != null) {
      map['children'] = _children?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// superior : "2eaa1749-2dcf-491b-9453-296d5864d033"
/// unitGuid : "af9ccbd5-743c-4bca-b140-721ca5d0de4d"
/// price : 200.0
/// grade : 2
/// name : "资助出国留学协议公证"

class Children {
  Children({
      String? superior,
      String? unitGuid,
      num? price,
      String? grade,
      String? name,}){
    _superior = superior;
    _unitGuid = unitGuid;
    _price = price;
    _grade = grade;
    _name = name;
}

  Children.fromJson(dynamic json) {
    _superior = json['superior'];
    _unitGuid = json['unitGuid'];
    _price = json['price'];
    _grade = json['grade'];
    _name = json['name'];
  }
  String? _superior;
  String? _unitGuid;
  num? _price;
  String? _grade;
  String? _name;
Children copyWith({  String? superior,
  String? unitGuid,
  num? price,
  String? grade,
  String? name,
}) => Children(  superior: superior ?? _superior,
  unitGuid: unitGuid ?? _unitGuid,
  price: price ?? _price,
  grade: grade ?? _grade,
  name: name ?? _name,
);
  String? get superior => _superior;
  String? get unitGuid => _unitGuid;
  num? get price => _price;
  String? get grade => _grade;
  String? get name => _name;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['superior'] = _superior;
    map['unitGuid'] = _unitGuid;
    map['price'] = _price;
    map['grade'] = _grade;
    map['name'] = _name;
    return map;
  }

}