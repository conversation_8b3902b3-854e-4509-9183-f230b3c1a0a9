import 'dart:convert';
/// unitGuid : 1
/// name : "中文（简体）"
/// zh : "中文（简体）"
/// en : "Simplified Chinese"
/// code : "zh-cn"

LanguageModel languageModelFromJson(String str) => LanguageModel.fromJson(json.decode(str));
String languageModelToJson(LanguageModel data) => json.encode(data.toJson());
class LanguageModel {
  LanguageModel({
    String? unitGuid,
      String? name, 
      String? zh, 
      String? en, 
      String? code,}){
    _unitGuid = unitGuid;
    _name = name;
    _zh = zh;
    _en = en;
    _code = code;
}

  LanguageModel.fromJson(dynamic json) {
    _unitGuid = json['unitGuid'];
    _name = json['name'];
    _zh = json['zh'];
    _en = json['en'];
    _code = json['code'];
  }
  String? _unitGuid;
  String? _name;
  String? _zh;
  String? _en;
  String? _code;
LanguageModel copyWith({  String? unitGuid,
  String? name,
  String? zh,
  String? en,
  String? code,
}) => LanguageModel(  unitGuid: unitGuid ?? _unitGuid,
  name: name ?? _name,
  zh: zh ?? _zh,
  en: en ?? _en,
  code: code ?? _code,
);
  String? get unitGuid => _unitGuid;
  String? get name => _name;
  String? get zh => _zh;
  String? get en => _en;
  String? get code => _code;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['unitGuid'] = _unitGuid;
    map['name'] = _name;
    map['zh'] = _zh;
    map['en'] = _en;
    map['code'] = _code;
    return map;
  }

}