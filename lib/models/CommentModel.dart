/// number : 0
/// deleteMark : 0
/// questionId : "8437716c-f43e-4c76-ad42-6d0298f874ff"
/// likesNumber : 0
/// isLike : 0
/// unitGuid : "8ef29794-d25b-4a56-96e7-0bef0be0fa1e"
/// commentId : ""
/// createUser : "2c21b457-2f6f-4414-9173-8da4ed2edcab"
/// commentContent : "OK酷"
/// type : 1
/// picture : 2
/// createDate : "2024-11-27 18:01:37"

class CommentModel {
  CommentModel({
    int? number,
    int? deleteMark,
    String? questionId,
    int? likesNumber,
    int? isLike,
    String? unitGuid,
    String? commentId,
    String? createUser,
    String? commentContent,
    int? type,
    String? picture,
    String? createDate,
    String? headIcon,
    String? userName,
  }) {
    number = number;
    deleteMark = deleteMark;
    questionId = questionId;
    likesNumber = likesNumber;
    isLike = isLike;
    unitGuid = unitGuid;
    commentId = commentId;
    createUser = createUser;
    commentContent = commentContent;
    type = type;
    picture = picture;
    createDate = createDate;
    headIcon = headIcon;
    userName = userName;
  }

  CommentModel.fromJson(dynamic json) {
    number = json['number'];
    deleteMark = json['deleteMark'];
    questionId = json['questionId'];
    likesNumber = json['likesNumber'];
    isLike = json['isLike'];
    unitGuid = json['unitGuid'];
    commentId = json['commentId'];
    createUser = json['createUser'];
    commentContent = json['commentContent'];
    type = json['type'];
    picture = json['picture'];
    createDate = json['createDate'];
    headIcon = json['cheadIcon'] ?? json['dheadIcon']??'';
    userName = json['cuserName']??json['duserName']??json["createUserName"]??'';
  }
  int? number;
  int? deleteMark;
  String? questionId;
  int? likesNumber;
  int? isLike;
  String? unitGuid;
  String? commentId;
  String? createUser;
  String? commentContent;
  int? type;
  String? picture;
  String? createDate;
  String? headIcon;
  String? userName;
  CommentModel copyWith({
    int? number,
    int? deleteMark,
    String? questionId,
    int? likesNumber,
    int? isLike,
    String? unitGuid,
    String? commentId,
    String? createUser,
    String? commentContent,
    int? type,
    String? picture,
    String? createDate,
    String? headIcon,
    String? userName,
  }) => CommentModel(
    number: number,
    deleteMark: deleteMark,
    questionId: questionId,
    likesNumber: likesNumber,
    isLike: isLike,
    unitGuid: unitGuid,
    commentId: commentId,
    createUser: createUser,
    commentContent: commentContent,
    type: type,
    picture: picture,
    createDate: createDate,
    headIcon: headIcon,
    userName: userName,
  );
  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['number'] = number;
    map['deleteMark'] = deleteMark;
    map['questionId'] = questionId;
    map['likesNumber'] = likesNumber;
    map['isLike'] = isLike;
    map['unitGuid'] = unitGuid;
    map['commentId'] = commentId;
    map['createUser'] = createUser;
    map['commentContent'] = commentContent;
    map['type'] = type;
    map['picture'] = picture;
    map['createDate'] = createDate;
    map['headIcon'] = headIcon;
    map['userName'] = userName;
    return map;
  }
}
