class NotaryOfficeInformation {
  String? unitGuid;
  String? notarialName;
  String? contactNumber;
  String? address;
  String? leader;
  String? onlineLeader;
  String? phone;
  String? notaryNumber;
  String? workTime;
  String? cityCode;
  int? enabledMark;
  int? deleteMark ;
  String? createDate;
  String? description;
  String? watermark;
  int? institutionType ;
  dynamic notarialId;
  String? organizationCode;
  dynamic creditCode;
  String? defaultPublicId;
  String? distance;
  String? cityName;
  String? provinceCode;
  String? provinceName;
  dynamic officeBusiness;
  String? officeCode;
  String? appId;
  String? appSecret;
  String? clientId;
  String? requestClientCode;
  String? fileClientCode;
  String? dataClientCode;
  String? sqClientId;
  String? sqSpClientId;
  dynamic isOnlinePay;

  NotaryOfficeInformation.fromJson(Map<String, dynamic> json) {
    address = json['address'];
    appId = json['appId'];
    appSecret = json['appSecret'];
    address = json['address'];
    cityCode = json['cityCode'];
    cityName = json['cityName'];
    createDate = json['createDate'];
    clientId = json['clientId'];
    creditCode = json['creditCode'];
    contactNumber = json['contactNumber'];
    dataClientCode = json['dataClientCode'];
    defaultPublicId = json['defaultPublicId'];
    deleteMark = json['deleteMark'];
    distance = json['distance'];
    enabledMark = json['enabledMark'];
    fileClientCode = json['fileClientCode'];
    institutionType = json['institutionType'];
    isOnlinePay = json['isOnlinePay'];
    leader = json['leader'];
    notarialId = json['notarialId'];
    notaryNumber = json['notaryNumber'];
    notarialName = json['notarialName'];
    officeBusiness = json['officeBusiness'];
    officeCode = json['officeCode'];
    onlineLeader = json['onlineLeader'];
    organizationCode = json['organizationCode'];
    phone = json['phone'];
    provinceCode = json['provinceCode'];
    provinceName = json['provinceName'];
    requestClientCode = json['requestClientCode'];
    sqClientId = json['sqClientId'];
    sqSpClientId  = json['sqSpClientId'];
    unitGuid = json['unitGuid'];
    watermark = json['watermark'];
    workTime = json['workTime'];
  }

 Map<String,dynamic> toMap() {
    Map<String,dynamic> json = {};
    json['address'] = address;
    json['appId'] =  appId;
    json['appSecret'] = appSecret;
    json['address'] = address;
    json['cityCode'] = cityCode;
    json['cityName'] = cityName;
    json['createDate'] = createDate;
    json['clientId'] = clientId;
    json['creditCode'] = creditCode;
    json['contactNumber'] = contactNumber;
    json['dataClientCode'] = dataClientCode;
    json['defaultPublicId'] = defaultPublicId;
    json['deleteMark'] = deleteMark;
    json['distance'] = distance;
    json['enabledMark'] = enabledMark;
    json['fileClientCode'] = fileClientCode;
    json['institutionType'] = institutionType;
    json['isOnlinePay'] = isOnlinePay;
    json['leader'] = leader;
    json['notarialId'] = notarialId;
    json['notaryNumber'] = notaryNumber;
    json['notarialName'] = notarialName;
    json['officeBusiness'] = officeBusiness;
    json['officeCode'] = officeCode;
    json['onlineLeader'] = onlineLeader;
    json['organizationCode'] = organizationCode;
    json['phone'] = phone;
    json['provinceCode'] = provinceCode;
    json['provinceName'] = provinceName;
    json['requestClientCode'] = requestClientCode;
    json['sqClientId'] = sqClientId;
    json['sqSpClientId'] = sqSpClientId;
    json['unitGuid'] = unitGuid;
    json['watermark'] = watermark;
    json['workTime'] = workTime;
    return json;
  }
}