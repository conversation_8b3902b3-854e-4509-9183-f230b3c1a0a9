/// isLike : 0
/// unitGuid : "cecc3e45-87569a-4c78-992b-487923f"
/// title : 877458965
/// content : "<p>1111</p>"
/// picture : "https://testgz.njguochu.com:33133/group1/M00/18/60/wKgx4WSRFKmALO8RAAA6QPREYTM92.jpeg"
/// isShow : 1
/// commentsNumber : 2
/// forwardingNumber : 0
/// deleteMark : 0
/// likesNumber : 0
/// notarialId : "45918866-aade-4760-81e1-d06ccb66045e"
/// notarialName : "测试公证处经纬度"
/// createDate : "2023-06-20 10:53:29"

class IndustryNewsModel {
  IndustryNewsModel({
    required  int isLike,
    required  String unitGuid,
    required  String title,
    required  String content,
    required  String picture,
    required  int isShow,
    required  int commentsNumber,
    required  int forwardingNumber,
    required  int deleteMark,
    required  int likesNumber,
    required  String notarialId,
    required  String notarialName,
    required  String createDate,}){
    isLike = isLike;
    unitGuid = unitGuid;
    title = title;
    content = content;
    picture = picture;
    isShow = isShow;
    commentsNumber = commentsNumber;
    forwardingNumber = forwardingNumber;
    deleteMark = deleteMark;
    likesNumber = likesNumber;
    notarialId = notarialId;
    notarialName = notarialName;
    createDate = createDate;
}

  IndustryNewsModel.fromJson(dynamic json) {
    isLike = json['isLike'];
    unitGuid = json['unitGuid'];
    title = json['title'];
    content = json['content'];
    picture = json['picture'];
    isShow = json['isShow'];
    commentsNumber = json['commentsNumber'];
    forwardingNumber = json['forwardingNumber'];
    deleteMark = json['deleteMark'];
    likesNumber = json['likesNumber'];
    notarialId = json['notarialId'];
    notarialName = json['notarialName'];
    createDate = json['createDate'];
  }
  int? isLike;
  String? unitGuid;
  String? title;
  String? content;
  String? picture;
  int? isShow;
  int? commentsNumber;
  int? forwardingNumber;
  int? deleteMark;
  int? likesNumber;
  String? notarialId;
  String? notarialName;
  String? createDate;
IndustryNewsModel copyWith({ required int isLike,
  required String unitGuid,
  required String title,
  required String content,
  required String picture,
  required int isShow,
  required int commentsNumber,
  required int forwardingNumber,
  required int deleteMark,
  required int likesNumber,
  required String notarialId,
  required String notarialName,
  required String createDate,
}) => IndustryNewsModel(  isLike: isLike ?? isLike,
  unitGuid: unitGuid ?? unitGuid,
  title: title ?? title,
  content: content ?? content,
  picture: picture ?? picture,
  isShow: isShow ?? isShow,
  commentsNumber: commentsNumber ?? commentsNumber,
  forwardingNumber: forwardingNumber ?? forwardingNumber,
  deleteMark: deleteMark ?? deleteMark,
  likesNumber: likesNumber ?? likesNumber,
  notarialId: notarialId ?? notarialId,
  notarialName: notarialName ?? notarialName,
  createDate: createDate ?? createDate,
);


  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['isLike'] = isLike;
    map['unitGuid'] = unitGuid;
    map['title'] = title;
    map['content'] = content;
    map['picture'] = picture;
    map['isShow'] = isShow;
    map['commentsNumber'] = commentsNumber;
    map['forwardingNumber'] = forwardingNumber;
    map['deleteMark'] = deleteMark;
    map['likesNumber'] = likesNumber;
    map['notarialId'] = notarialId;
    map['notarialName'] = notarialName;
    map['createDate'] = createDate;
    return map;
  }

}