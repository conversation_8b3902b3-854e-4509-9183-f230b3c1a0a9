
class NotaryOfficeModel {
  final String name;
  final String address;
  final double distance;
  final String phone;

  NotaryOfficeModel({
    required this.name,
    required this.address,
    required this.distance,
    required this.phone,
  });

  factory NotaryOfficeModel.fromJson(Map<String, dynamic> json) {
    return NotaryOfficeModel(
      name: json['name'],
      address: json['address'],
      distance: json['distance'],
      phone: json['phone'],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'address': address,
      'distance': distance,
      'phone': phone,
    };
  }
}
