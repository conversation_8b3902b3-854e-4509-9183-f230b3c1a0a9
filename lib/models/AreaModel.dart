import 'dart:convert';
/// unitGuid : 37
/// en : "China"
/// zh : "中国"
/// code : "86"
/// locale : "CN"
/// preg : ""
/// number : null

AreaModel areaModelFromJson(String str) => AreaModel.fromJson(json.decode(str));
String areaModelToJson(AreaModel data) => json.encode(data.toJson());
class AreaModel {
  AreaModel({
    String? unitGuid,
      String? en, 
      String? zh,
      int? code,
      String? locale, 
      String? preg, 
      dynamic number,}){
    _unitGuid = unitGuid;
    _en = en;
    _zh = zh;
    _code = code;
    _locale = locale;
    _preg = preg;
    _number = number;
}

  AreaModel.fromJson(dynamic json) {
    _unitGuid = json['unitGuid'];
    _en = json['en'];
    _zh = json['zh'];
    _code = json['code'];
    _locale = json['locale'];
    _preg = json['preg'];
    _number = json['number'];
  }
  String? _unitGuid;
  String? _en;
  String? _zh;
  int? _code;
  String? _locale;
  String? _preg;
  dynamic _number;
AreaModel copyWith({  String? unitGuid,
  String? en,
  String? zh,
  int? code,
  String? locale,
  String? preg,
  dynamic number,
}) => AreaModel(  unitGuid: unitGuid ?? _unitGuid,
  en: en ?? _en,
  zh: zh ?? _zh,
  code: code ?? _code,
  locale: locale ?? _locale,
  preg: preg ?? _preg,
  number: number ?? _number,
);
  String? get unitGuid => _unitGuid;
  String? get en => _en;
  String? get zh => _zh;
  int? get code => _code;
  String? get locale => _locale;
  String? get preg => _preg;
  dynamic get number => _number;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['unitGuid'] = _unitGuid;
    map['en'] = _en;
    map['zh'] = _zh;
    map['code'] = _code;
    map['locale'] = _locale;
    map['preg'] = _preg;
    map['number'] = _number;
    return map;
  }

}