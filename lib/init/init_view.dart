import 'package:bloc_test/init/init_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'init_bloc.dart';
import 'init_event.dart';


class InitPage extends StatelessWidget {
  const InitPage({super.key, });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => InitBloc()..add(CountEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<InitBloc>(context);
    return Scaffold(
      appBar: AppBar(title: Text("bloc-bloc范例"),),
      body: BlocBuilder<InitBloc, InitState>(
        builder: (context, state) {
          return Center(child: Text('${state.count}'));
        }
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => bloc.add(CountEvent()),
        tooltip: 'Increment',
        child: const Icon(Icons.add),
      ),
    );
  }
}

