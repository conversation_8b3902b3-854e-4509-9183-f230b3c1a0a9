class CodeLoginState {
  final String phoneNumber;
  final String verificationCode;
  final bool isLoading;
  final String? errorMessage;
  final bool canGetCode;
  final bool isSendingCode;
  final String countdownString;
  final bool privacyChecked;

  CodeLoginState({
    this.phoneNumber = '',
    this.verificationCode = '',
    this.isLoading = false,
    this.errorMessage,
    this.canGetCode = true,
    this.isSendingCode = false,
    this.privacyChecked = false,
    this.countdownString = "获取验证码",
  });

  CodeLoginState init() {
    return CodeLoginState();
  }

  CodeLoginState clone() {
    return CodeLoginState(
      phoneNumber: phoneNumber,
      verificationCode: verificationCode,
      isLoading: isLoading,
      errorMessage: errorMessage,
      canGetCode: canGetCode,
      isSendingCode: isSendingCode,
      countdownString: countdownString,
      privacyChecked: privacyChecked,
    );
  }

  CodeLoginState copyWith({
    String? phoneNumber,
    String? verificationCode,
    bool? isLoading,
    String? errorMessage,
    bool? canGetCode,
    bool? isSendingCode,
    String? countdownString,
    bool? privacyChecked,
  }) {
    return CodeLoginState(
      phoneNumber: phoneNumber ?? this.phoneNumber,
      verificationCode: verificationCode ?? this.verificationCode,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      canGetCode: canGetCode ?? this.canGetCode,
      isSendingCode: isSendingCode ?? this.isSendingCode,
      countdownString: countdownString ?? this.countdownString,
      privacyChecked: privacyChecked ?? this.privacyChecked,
    );
  }

  bool get canLogin {
    return phoneNumber.isNotEmpty &&
        verificationCode.isNotEmpty && privacyChecked;
  }
}
