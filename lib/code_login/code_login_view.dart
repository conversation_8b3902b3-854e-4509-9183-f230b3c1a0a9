import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/appTheme.dart';
import '../utils/AppApplication.dart';
import '../utils/router.dart';
import '../utils/StrUtil.dart';
import '../utils/toast_util.dart';

import 'code_login_bloc.dart';
import 'code_login_event.dart';
import 'code_login_state.dart';

class CodeLoginPage extends StatelessWidget {
  const CodeLoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) =>
      CodeLoginBloc()
        ..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = context.read<CodeLoginBloc>();
    return Scaffold(
      appBar: AppBar(title: Text('验证码登录')),
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            height: double.infinity,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  // 顶部插画
                  SizedBox(
                    height: 180.h,
                    width: double.infinity,
                    child: Image.asset(
                      'images/logoimg.png',
                      fit: BoxFit.contain,
                    ),
                  ),

                  SizedBox(height: 20.h),

                  // 手机号输入框
                  _buildInputField(
                    hint: '请输入账号',
                    value: "",
                    keyboardType: TextInputType.phone,
                    onChanged:
                        (value) =>
                        bloc.add(PhoneNumberChangedEvent(value)),
                  ),

                  SizedBox(height: 20.h),

                  // 验证码输入框
                  _buildVerificationCodeField(
                      context, bloc),

                  SizedBox(height: 10.h),

                  // 账号密码登录/忘记密码
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop(); // 返回账号密码登录
                        },
                        child: Text(
                          '账号密码登录',
                          style: TextStyle(
                            color: AppTheme.textBlack_1,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          AppApplication.getCurrentState()?.pushNamed(
                            RoutePaths.modifyPsd,
                          );
                        },
                        child: Text(
                          '忘记密码?',
                          style: TextStyle(
                            color: AppTheme.textBlack_1,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 20.h),

                  // 登录按钮
                  BlocBuilder<CodeLoginBloc, CodeLoginState>(
                    builder: (context, state) {
                      return _buildLoginButton(context, state, bloc);
                    },
                  ),

                  SizedBox(height: 10.h),

                  // 注册
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '没有账号？',
                        style: TextStyle(
                          color: AppTheme.textBlack_1,
                          fontSize: 14.sp,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          AppApplication.getCurrentState()?.pushNamed(
                            RoutePaths.register,
                            arguments: {
                              'unionId': "",
                              'appleId': "",
                              'mobile': "",
                              'loginType': 3,
                            },
                          );
                        },
                        child: Text(
                          '立即注册',
                          style: TextStyle(
                            color: AppTheme.themeBlue,
                            fontSize: 14.sp,
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 60.h),
                ],
              ),
            ),
          ),
          BlocBuilder<CodeLoginBloc, CodeLoginState>(
            builder: (context, state) {
              return _buildPrivacyAgreement(context, state, bloc);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required String hint,
    required String value,
    required Function(String) onChanged,
    TextInputType? keyboardType,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              "账号",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300, width: 1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            keyboardType: keyboardType,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14.sp,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              onChanged(value);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationCodeField(BuildContext context,
      CodeLoginBloc bloc,) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '验证码',
              style: TextStyle(
                color: AppTheme.textBlack,
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300, width: 1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  keyboardType: TextInputType.number,
                  onChanged:
                      (value) => bloc.add(VerificationCodeChangedEvent(value)),
                  decoration: InputDecoration(
                    hintText: '请输入短信验证码',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 14.sp,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(width: 15.w),
            BlocBuilder<CodeLoginBloc, CodeLoginState>(
              builder: (context, state) {
                return SizedBox(
                  width: 120.w,
                  height: 40.h,
                  child: ElevatedButton(
                    onPressed:
                    state.canGetCode && !state.isSendingCode
                        ? () => bloc.add(GetVerificationCodeEvent())
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                      state.canGetCode
                          ? AppTheme.themeBlue
                          : Colors.grey.shade300,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                    child:
                    state.isSendingCode
                        ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white,
                        ),
                      ),
                    )
                        : Text(
                      state.countdownString ?? '',
                      style: TextStyle(
                        color:
                        state.canGetCode
                            ? Colors.white
                            : AppTheme.textBlack_1,
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLoginButton(BuildContext context,
      CodeLoginState state,
      CodeLoginBloc bloc,) {
    return SizedBox(
      width: double.infinity,
      height: 50.h,
      child: ElevatedButton(
        onPressed:
        state.canLogin && !state.isLoading
            ? () => bloc.add(LoginSubmittedEvent())
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.themeBlue,
          disabledBackgroundColor: AppTheme.bgB,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.r),
          ),
          elevation: 0,
        ),
        child:
        state.isLoading
            ? SizedBox(
          width: 20.w,
          height: 20.w,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        )
            : Text(
          '立即登录',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildPrivacyAgreement(BuildContext context,
      CodeLoginState state,
      CodeLoginBloc bloc,) {
    return SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Row(
          children: [
            Checkbox(
              value: state.privacyChecked,
              onChanged:
                  (value) => bloc.add(PrivacyCheckedChangedEvent(value!)),
              activeColor: AppTheme.themeBlue,
            ),
            Expanded(
              child: RichText(
                text: TextSpan(
                  text: "登录即代表您已阅读并同意",
                  style: TextStyle(fontSize: 12.sp, color: AppTheme.textBlack),
                  children: [
                    TextSpan(
                      text: "《青桐智盒隐私政策》",
                      style: TextStyle(color: AppTheme.themeBlue),
                      recognizer:
                      TapGestureRecognizer()
                        ..onTap = () {
                          AppApplication.getCurrentState()?.pushNamed(
                            RoutePaths.webViewWidget,
                            arguments: {
                              'title': "青桐智盒隐私政策",
                              'url': StrUtil.privacyPolicy,
                            },
                          );
                        },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
