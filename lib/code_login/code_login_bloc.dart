import 'dart:async';
import 'dart:convert';
import 'package:bloc/bloc.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/sp_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import '../models/userInfo.dart';
import '../utils/block_puzzle_captcha.dart';
import '../utils/router.dart';
import '../utils/toast_util.dart';
import 'code_login_event.dart';
import 'code_login_state.dart';

class CodeLoginBloc extends Bloc<CodeLoginEvent, CodeLoginState> {
  Timer? _countdownTimer;

  CodeLoginBloc() : super(CodeLoginState().init()) {
    on<InitEvent>(_init);
    on<PhoneNumberChangedEvent>(_onPhoneNumberChanged);
    on<VerificationCodeChangedEvent>(_onVerificationCodeChanged);
    on<GetVerificationCodeEvent>(_onGetVerificationCode);
    on<PrivacyCheckedChangedEvent>(_onPrivacyCheckedChanged);
    on<LoginSubmittedEvent>(_onLoginSubmitted);
    on<CountdownTickEvent>(_onCountdownTick);
    on<ResetVerificationCodeEvent>(_resetVerificationCodeEvent);
    on<ResetLoginStateEvent>(_resetLoginStateEvent);
    on<StartTimerEvent>(_startTimerEvent);
  }

  void _init(InitEvent event, Emitter<CodeLoginState> emit) async {
    emit(state.clone());
  }

  void _onPhoneNumberChanged(
    PhoneNumberChangedEvent event,
    Emitter<CodeLoginState> emit,
  ) {
    emit(state.copyWith(phoneNumber: event.phoneNumber, errorMessage: null));
  }

  void _onVerificationCodeChanged(
    VerificationCodeChangedEvent event,
    Emitter<CodeLoginState> emit,
  ) {
    emit(state.copyWith(verificationCode: event.code, errorMessage: null));
  }

  void _onGetVerificationCode(
    GetVerificationCodeEvent event,
    Emitter<CodeLoginState> emit,
  ) async {
    if (!state.canGetCode || state.phoneNumber.isEmpty) {
      emit(state.copyWith(errorMessage: '请输入手机号'));
      return;
    }

    emit(state.copyWith(isSendingCode: true));
    getImageCode();
  }

  void _onPrivacyCheckedChanged(
    PrivacyCheckedChangedEvent event,
    Emitter<CodeLoginState> emit,
  ) {
    emit(state.copyWith(privacyChecked: event.checked));
    SpUtil.instance.setBool(StrUtil.isAgree, event.checked);
  }

  void _onLoginSubmitted(
    LoginSubmittedEvent event,
    Emitter<CodeLoginState> emit,
  ) async {
    if (!state.canLogin) return;
    if (state.phoneNumber.isEmpty || state.verificationCode.isEmpty) {
      ToastUtil.showWarningToast("用户名或验证码不能为空！");
      return;
    }
    if(!state.privacyChecked){
      ToastUtil.showWarningToast("请先同意隐私协议");
      return;
    }
    emit(state.copyWith(isLoading: true));
  }

  void _onCountdownTick(
    CountdownTickEvent event,
    Emitter<CodeLoginState> emit,
  ) async {
    if (event.countdown <= 0) {
      emit(state.copyWith(canGetCode: true, countdownString: "获取验证码"));
    } else {
      emit(state.copyWith(countdownString: "${event.countdown} S"));
    }
  }

  void _resetVerificationCodeEvent(
    ResetVerificationCodeEvent event,
    Emitter<CodeLoginState> emit,
  ) async {
    _countdownTimer?.cancel();
    emit(state.copyWith(countdownString: "获取验证码",canGetCode: true,isSendingCode: false));
  }

  void _resetLoginStateEvent(
    ResetLoginStateEvent event,
    Emitter<CodeLoginState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: false,
        errorMessage: null,
      ),
    );
  }

  void _startTimerEvent(
    StartTimerEvent event,
    Emitter<CodeLoginState> emit,
  ) async {
    _countdownTimer?.cancel();
    int countdown = 120;
    emit(state.copyWith(canGetCode: false,isSendingCode: false,countdownString: " 119 S"));
    _countdownTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      countdown--;
      add(CountdownTickEvent(countdown));

      if (countdown <= 0) {
        timer.cancel();
      }
    });
  }

  /// 获取短信验证码
  getImageCode() {
    showDialog<Null>(
      context: AppApplication.currentContext!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BlockPuzzleCaptchaPage(
          onSuccess: (v, token) {
            getCode(v, token);
          },
          onFail: () {
            if (state.isSendingCode) {
              add(
                ResetVerificationCodeEvent(
                  canGetCode: true,
                  isSendingCode: false,
                ),
              );
            }
          },
          close: () {
            if (state.isSendingCode) {
              add(
                ResetVerificationCodeEvent(
                  canGetCode: true,
                  isSendingCode: false,
                ),
              );
              add(ResetVerificationCodeEvent(canGetCode: true,isSendingCode: false));
            }
          },
        );
      },
    );
  }

  /// 获取验证码
  getCode(String v, String token) async {
    Map<String, String> map = {
      "areaCode": "86",
      "mobile": state.phoneNumber,
      "sendType": "2",
      'type': "2",
      "pointJson": v,
      "token":token
    };
    final response = await ApiInstance().get(
      '${StrUtil.userModule}/sys/Sms/abroadSend',
      queryParameters: map,
      errorFunction: (error) {
        add(ResetVerificationCodeEvent(canGetCode: true, isSendingCode: false));
      },
    );
    if (response != null) {
      if (response["code"] != 200) {
        ToastUtil.showWarningToast(
          "${response["data"] ?? response["message"]}",

        );
        add(ResetVerificationCodeEvent(canGetCode: true, isSendingCode: false));
        return;
      }
      add(StartTimerEvent());
      AppApplication.pop();
      ToastUtil.showSuccessToast("验证码获取成功！");
    } else {
      ToastUtil.showWarningToast("获取失败，稍后再试！");
      add(ResetVerificationCodeEvent(canGetCode: true, isSendingCode: false));
    }
  }

  /// 登录
  login() async {
    Map<String, Object> map = {
      "smsCode": state.verificationCode,
      "source": "1",
      'areaCode': "86",
      'mobile': state.phoneNumber,
      "type": "2"
    };
    EasyLoading.show(status: "登录中...");
    final response = await ApiInstance().post(
      "${StrUtil.userModule}/sys/login/obAuthCodeNewApp",
      data: map,
      errorFunction: (error) {
        EasyLoading.dismiss();
        add(ResetLoginStateEvent());
      },
    );

    if (response != null) {
      if (response["code"] != 200) {
        EasyLoading.dismiss();
        ToastUtil.showWarningToast(
          "${response["data"] ?? response["message"]}",
        );
        add(ResetLoginStateEvent());
        return;
      }
      await getUserInformation(response['data']['token'], response["data"]['role'][0]['roleId']);
    } else {
      EasyLoading.dismiss();
      ToastUtil.showWarningToast("登录失败，请稍后再试！");
      add(ResetLoginStateEvent());
    }
  }

  /// 获取用户信息
  getUserInformation(String token,String roleId) async {
    final response = await ApiInstance().get(
      "${StrUtil.userModule}/cgz/user/getUserInfo",
      queryParameters: {
        "roleId":roleId
      },
      options: Options(
        headers: {
          "token":token
        }
      ),
      errorFunction: (error) {
        EasyLoading.dismiss();
        add(ResetLoginStateEvent());
      },
    );
    EasyLoading.dismiss();
    if (response != null) {
      if (response["code"] != 200) {
        ToastUtil.showWarningToast(
          "${response["data"] ?? response["message"]}",
        );
        add(ResetLoginStateEvent());
        return;
      }
      SpUtil.instance.remove(StrUtil.userInfo);
      response['data']['token'] = token;
      UserInfoEntity userInfoEntity = UserInfoEntity.fromJson(response['data']);
      AppApplication.getInstance().userInfoEntity = userInfoEntity;
      SpUtil.instance.setString(StrUtil.userInfo,jsonEncode(userInfoEntity.toJson()));
      AppApplication.getCurrentState()?.pushNamedAndRemoveUntil(RoutePaths.mainIndex, (_)=>false);
    } else {
      ToastUtil.showWarningToast("登录失败，请稍后再试！");
      add(ResetLoginStateEvent());
    }
  }
  @override
  Future<void> close() {
    _countdownTimer?.cancel();
    return super.close();
  }
}
