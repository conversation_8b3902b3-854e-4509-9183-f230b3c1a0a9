abstract class CodeLoginEvent {}

class InitEvent extends CodeLoginEvent {}

/// 手机号输入框内容改变
class PhoneNumberChangedEvent extends CodeLoginEvent {
  final String phoneNumber;
  PhoneNumberChangedEvent(this.phoneNumber);
}

/// 验证码输入框内容改变
class VerificationCodeChangedEvent extends CodeLoginEvent {
  final String code;
  VerificationCodeChangedEvent(this.code);
}

/// 获取验证码
class GetVerificationCodeEvent extends CodeLoginEvent {}

/// 隐私协议勾选状态改变
class PrivacyCheckedChangedEvent extends CodeLoginEvent {
  final bool checked;
  PrivacyCheckedChangedEvent(this.checked);
}

/// 登录按钮点击
class LoginSubmittedEvent extends CodeLoginEvent {}

/// 倒计时事件
class CountdownTickEvent extends CodeLoginEvent {
  final int countdown;
  CountdownTickEvent(this.countdown);
}

/// 触发定时器
class StartTimerEvent extends CodeLoginEvent {}

/// 重置短信验证码按钮
class ResetVerificationCodeEvent extends CodeLoginEvent {
  final bool canGetCode;
  final bool isSendingCode;
  ResetVerificationCodeEvent({
    required this.canGetCode,
    required this.isSendingCode,
  });
}

/// 重置登录状态
class ResetLoginStateEvent extends CodeLoginEvent {}