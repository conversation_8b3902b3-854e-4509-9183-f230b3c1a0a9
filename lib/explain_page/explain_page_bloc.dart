import 'package:bloc/bloc.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'explain_page_event.dart';
import 'explain_page_state.dart';

class ExplainPageBloc extends Bloc<ExplainPageEvent, ExplainPageState> {
  ExplainPageBloc() : super(ExplainPageState().init()) {
    on<InitEvent>(_init);
    on<AgentChooseEvent>(_agentChoose);
    on<CheckBoxEvent>(_checkBoxEvent);
    on<JumpIntoOtherPageEvent>(_jumpIntoOtherPage);
  }

  void _init(InitEvent event, Emitter<ExplainPageState> emit) async {
    state.argument = event.arguments;
    // 不要在 state 中存储 context
    logger.d("context-----------${event.context}");
    emit(state.clone());
    if(state.argument == 1){
      // 延迟执行，确保页面已经构建完成
      Future.delayed(const Duration(milliseconds: 100), () {
        state.context = event.context;
        showAgentAlert(state.context!);
      });
    }
  }

  void _agentChoose(AgentChooseEvent event, Emitter<ExplainPageState> emit) async {
    state.isAgent = event.agentChoose;
    emit(state.clone());
  }

  void _checkBoxEvent(CheckBoxEvent event, Emitter<ExplainPageState> emit) async {
    state.checkState = event.checkState;
    emit(state.clone());
  }

  void _jumpIntoOtherPage(JumpIntoOtherPageEvent event, Emitter<ExplainPageState> emit) async {
    emit(state.clone());
    switch(state.argument){
      case 1:
        AppApplication.getCurrentState()?.pushNamed(RoutePaths.chooseNotaryItem,arguments: state.isAgent ? 1 : 0);
        break;
      case 2:
        AppApplication.getCurrentState()?.pushReplacementNamed(RoutePaths.video);
        break;
      case 3:
        AppApplication.getCurrentState()?.pushReplacementNamed(RoutePaths.protocol);
        break;
    }
  }

  /// 是否是代理人弹窗
  showAgentAlert(BuildContext context){
    // 检查 context 是否仍然有效
    if (!context.mounted) {
      logger.w("Context is not mounted, cannot show dialog");
      return;
    }

    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext){
          return Column(
            children: [
              const Spacer(flex:1),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                margin: EdgeInsets.symmetric(horizontal: 20.w),
                constraints: BoxConstraints(
                  maxHeight: 200.h,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.white,
                  borderRadius: BorderRadius.circular(16.r),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.darkGrey.withAlpha(200),
                      offset: Offset(0, 2),
                      blurRadius: 10
                    )
                  ]
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(padding: EdgeInsets.symmetric(vertical: 20.h),child: Text("请选择身份类型",style: TextStyle(fontSize: 16.sp,fontWeight: FontWeight.bold,color: AppTheme.textBlack),),),
                    Text('''您是否代他人办理公证事项，如果是请选择“我是代理人”，不是则选择“我是申请人”''',style: TextStyle(
                      fontSize: 15.sp,
                      color: AppTheme.darkGrey,
                    ),),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 15.h),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextButton(
                            onPressed: (){
                              add(AgentChooseEvent(false));
                              AppApplication.pop();
                            },
                            style: ButtonStyle(
                              padding: WidgetStatePropertyAll(EdgeInsets.symmetric(horizontal: 20.w)),
                              backgroundColor: WidgetStatePropertyAll(AppTheme.themeBlue)
                            ),
                            child: Text("我是申请人",style: TextStyle(
                              fontSize: 15.sp,
                              color: AppTheme.white,
                            ),),
                          ),
                          SizedBox(width: 20.w,),
                          TextButton(
                              onPressed: (){
                                add(AgentChooseEvent(true));
                                AppApplication.pop();
                              },
                              style: ButtonStyle(
                                padding: WidgetStatePropertyAll(EdgeInsets.symmetric(horizontal: 20.w)),
                                backgroundColor: WidgetStatePropertyAll(AppTheme.darkGrey.withAlpha(100)),

                              ),
                              child: Text("我是代理人",style: TextStyle(fontSize: 15.sp,color: AppTheme.white),))
                        ],
                      ),
                    )

                  ],
                ),

              ),
              const Spacer(flex: 1,)
            ],
          );

    });
  }
}
