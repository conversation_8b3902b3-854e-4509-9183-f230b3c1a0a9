import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/router.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'explain_page_bloc.dart';
import 'explain_page_event.dart';
import 'explain_page_state.dart';

class ExplainPage extends StatelessWidget {
  final int arguments;
  const ExplainPage({super.key, required this.arguments});

  @override
  Widget build(BuildContext myContext) {
    return BlocProvider(
      create:
          (BuildContext context) => ExplainPageBloc()..add(InitEvent(arguments,myContext)),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<ExplainPageBloc>(context);
    return Scaffold(
      appBar: AppBar(title: Text('申办流程及说明'), centerTitle: true),
      body: BlocBuilder<ExplainPageBloc, ExplainPageState>(
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.all(10.r),
                child: const Text(
                  "申办流程",
                  style: TextStyle(fontWeight: FontWeight.w700),
                ),
              ),
              arguments == 1
                  ? _buildArgumentsPageOne()
                  : _buildArgumentsPageTwo(),
              Container(
                margin: EdgeInsets.symmetric(vertical: 10.r),
                color: AppTheme.bgB,
                height: 15.h,
              ),
              Padding(
                padding: EdgeInsets.only(
                  left: 10.w,
                  right: 10.w,
                  bottom: 10.w,
                ),
                child: Text(
                  "申办说明",
                  style: TextStyle(fontWeight: FontWeight.w700),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(left: 15.w, right: 15.w),
                  child: Text(
                    '    一、当事人向公证处申办公证，应如实告知所申请办理公证事项的有关情况，提供真实、合法、充分的证明材料。\n'
                        '    二、当事人在网上上传的相关证明材料的原件需要在领取公证书之日向公证处公证人员出示并核对。根据《中华人民共和国公证法》、《公证程序规则》等有关规定，当事人提供虚假证明材料骗取公证书的，将可能依法被追究民事责任、行政责任，甚至刑事责任。\n'
                        '    三、如当事人虚构、隐瞒事实，或者提供虚假证明材料；提供的证明材料不充分或者拒绝补充证明材料；申请公证的事项不真实、不合法或是拒绝按照规定支付公证费等，本处将不予办理公证。不予办理公证的，公证处将根据不予办理的原因及责任，酌情退还部分或者全部收取的公证费。',
                    style: TextStyle(fontSize: 14.sp),
                  ),
                ),
              ),
              Column(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: 15.w,
                      right: 15.w,
                      top: 25.h,
                    ),
                    child: RichText(
                      maxLines: 2,
                      text: TextSpan(
                        text: '《在线受理服务使用规则》',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: AppTheme.themeBlue,
                        ),
                        recognizer:
                        TapGestureRecognizer()
                          ..onTap = () {
                            Navigator.pushNamed(
                              context,
                              RoutePaths.ruleDetail,
                            );
                          },
                        children: [
                          TextSpan(
                            text: '《电子签名服务告知条款》',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppTheme.themeBlue,
                            ),
                            recognizer:
                            TapGestureRecognizer()
                              ..onTap = () {
                                Navigator.pushNamed(
                                  context,
                                  RoutePaths.dianZiName,
                                );
                              },
                          ),
                        ],
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Checkbox(
                        value: state.checkState,
                        activeColor: AppTheme.themeBlue,
                        onChanged: (value) {
                          bloc.add(CheckBoxEvent(value!));
                        },
                      ),
                      Text(
                        "已阅读并同意上述规则和条款",
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    ],
                  ),
                  Padding(
                    padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom,left: 15.w,right: 15.w),
                    child: SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        style: ButtonStyle(
                          backgroundColor: WidgetStateProperty.all(
                            AppTheme.themeBlue,
                          ),
                        ),
                        onPressed: state.checkState ?() {
                          bloc.add(JumpIntoOtherPageEvent());
                        } :null,
                        child: Text(
                          "开始申办",
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: AppTheme.nearlyWhite,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  // arguments == 1
  Widget _buildArgumentsPageOne() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 15.r, right: 15.r, bottom: 15.r),
          child: Image.asset("images/flow_path_top.png", fit: BoxFit.fill),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildColumnItem("选择", "公证事项"),
            _buildColumnItem("填写", "申请信息"),
            _oneItem("上传材料"),
            _oneItem("支付"),
            _oneItem("等待出证"),
          ],
        ),
      ],
    );
  }

  // arguments == 2
  Widget _buildArgumentsPageTwo() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 15.r, right: 15.r, bottom: 15.r),
          child: Image.asset("images/ic_process.png", fit: BoxFit.fill),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _oneItem("公证申请"),
            _oneItem("公证申请"),
            _oneItem("谈话及初审"),
            _oneItem("检查及出证"),
            _oneItem("公证书受领"),
          ],
        ),
      ],
    );
  }

  // Column Item
  Widget _buildColumnItem(String title, String content) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 60.w,
          child: Text(
            title,
            textAlign: TextAlign.center,
            maxLines: 2,
            style: TextStyle(fontSize: 11.sp),
          ),
        ),
        SizedBox(
          width: 60.w,
          child: Text(
            content,
            textAlign: TextAlign.center,
            maxLines: 2,
            style: TextStyle(fontSize: 11.sp),
          ),
        ),
      ],
    );
  }

  // one item
  Widget _oneItem(String content){
    return SizedBox(
      width: 60.w,
      child: Text(
        content,
        textAlign: TextAlign.center,
        maxLines: 2,
        style: TextStyle(fontSize: 11.sp),
      ),
    );
  }
}
