import 'package:flutter/cupertino.dart';

abstract class ExplainPageEvent {}

class InitEvent extends ExplainPageEvent {
  final int arguments;
  BuildContext context;
  InitEvent(this.arguments,this.context);
}

class CheckBoxEvent extends ExplainPageEvent {
  final bool checkState;
  CheckBoxEvent(this.checkState);
}

class AgentChooseEvent extends ExplainPageEvent {
  final bool agentChoose;
  AgentChooseEvent(this.agentChoose);
}

class JumpIntoOtherPageEvent extends ExplainPageEvent {
  JumpIntoOtherPageEvent();
}
