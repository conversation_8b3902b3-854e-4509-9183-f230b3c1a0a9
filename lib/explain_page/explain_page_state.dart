import 'package:flutter/cupertino.dart';

class ExplainPageState {

  int? argument;
  bool checkState = false;
  bool isAgent = false;
  BuildContext? context;

  ExplainPageState init() {
    return ExplainPageState()
    ..checkState = false
      ..context = null
    ..argument = null;
  }

  ExplainPageState clone() {
    return ExplainPageState()
    ..checkState = checkState
      ..context = null
      ..isAgent = isAgent
    ..argument = argument;
  }
}
