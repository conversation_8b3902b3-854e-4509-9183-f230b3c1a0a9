import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/CommentModel.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'news_event.dart';
import 'news_state.dart';

class NewsBloc extends Bloc<NewsEvent, NewsState> {
  RefreshController refreshController = RefreshController();
  RefreshController? replyController;
  NewsBloc() : super(NewsState().init()) {
    on<InitEvent>(_init);
    on<AddCommentEvent>(_addCommentEvent);
    on<LikeTapEvent>(_likeTapEvent);
    on<CommentRefreshEvent>(_commentRefreshEvent);
    on<CommentLoadMoreEvent>(_commentLoadMoreEvent);
    on<ReplyRefreshEvent>(_replyRefreshEvent);
    on<ReplyLoadMoreEvent>(_replyLoadMoreEvent);
    on<ReleaseCommentAndReply>(_releaseCommentAndReplyEvent);
    on<GetReplyListEvent>(_getReplyListEvent);
  }

  void _init(InitEvent event, Emitter<NewsState> emit) async {
    emit(state.clone()..model = event.model);
    emit(state.clone()..context = event.context);
    List<CommentModel> temp = await getCommentList(event, emit);
    emit(state.clone()..commentList.addAll(temp));
  }

  void _addCommentEvent(AddCommentEvent event, Emitter<NewsState> emit) async {
    emit(state.clone()..type = event.type);
    showCommentDialog(event, emit);

  }
  void _likeTapEvent(LikeTapEvent event, Emitter<NewsState> emit) async {
    emit(state.clone());
    clickLike(event, emit);
  }
  void _commentRefreshEvent(CommentRefreshEvent event, Emitter<NewsState> emit) async {
    state.commentPageNumber = 1;
    List<CommentModel> temp = await getCommentList(event, emit);
    emit(state.clone()..commentList.addAll(temp));
  }
  void _commentLoadMoreEvent(CommentLoadMoreEvent event, Emitter<NewsState> emit) async {
    state.commentPageNumber++;
    List<CommentModel> temp = await getCommentList(event, emit);
    emit(state.clone()..commentList.addAll(temp));
  }
  void _replyRefreshEvent(ReplyRefreshEvent event, Emitter<NewsState> emit) async {
    state.replyPageNumber = 1;
    List<CommentModel> tempList = await getReplyList(event, emit);
    emit(state.clone()..replyList.addAll(tempList));
  }
  void _replyLoadMoreEvent(ReplyLoadMoreEvent event, Emitter<NewsState> emit) async {
    state.replyPageNumber++;
    List<CommentModel> tempList = await getReplyList(event, emit);
    emit(state.clone()..replyList.addAll(tempList));
  }
  void _releaseCommentAndReplyEvent(ReleaseCommentAndReply event, Emitter<NewsState> emit) async {
    emit(state.clone()..commentContent = event.commentContent);
    addComment(event, emit);
  }
  void _getReplyListEvent(GetReplyListEvent event, Emitter<NewsState> emit) async {
    List<CommentModel> tempList = await getReplyList(event, emit);
    emit(state.clone()..replyList.addAll(tempList));
   showReplyListDialog(event, emit);
  }

  /// 获取评论列表
  Future<List<CommentModel>> getCommentList(NewsEvent event,Emitter<NewsState> emit) async {
    Map<String,dynamic> params = {
      "currentPage":state.commentPageNumber,
      "pageSize":state.pageSize,
      "questionId":state.model?.unitGuid,
    };
    EasyLoading.show();
    final response = await ApiInstance().post("${StrUtil.parkModule}/sq/comment/commentPage",data: params,errorFunction: (error) {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    });
    EasyLoading.dismiss();
    if (response['code'] == 200 && response['items'] != null) {
      List tempList = response['items'];
      if (state.commentPageNumber == 1){
        state.commentList.clear();
        refreshController.refreshCompleted();
      } else {
        refreshController.loadComplete();
      }
      List<CommentModel> temp = [];
      for (var item in tempList) {
        temp.add(CommentModel.fromJson(item));
      }
     return temp;
    } else {
      ToastUtil.showErrorToast(response["msg"]??response["message"]??response["data"]);
      return [];
    }
  }

  /// 获取回复列表
  getReplyList(NewsEvent event,Emitter<NewsState> emit) async {
    Map<String,dynamic> params = {
      "currentPage":state.replyPageNumber,
      "pageSize":state.pageSize,
      "commentId":state.commentModel?.unitGuid,
      "questionId":state.model?.unitGuid,
    };
    EasyLoading.show();
    final response = await ApiInstance().post("${StrUtil.parkModule}/sq/comment/replyPage",data: params,errorFunction: (error) {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    });
    EasyLoading.dismiss();
    if (response['code'] == 200 && response['items'] != null) {
      List tempList = response['items'];
      if (state.replyPageNumber == 1){
        state.replyList.clear();
        replyController?.refreshCompleted();
      } else {
        replyController?.loadComplete();
      }
      List<CommentModel> temp = [];
      for (var element in tempList) {
        temp.add(CommentModel.fromJson(element));
      }
      return temp;
    } else {
      return  [];
      ToastUtil.showErrorToast(response["msg"]??response["message"]??response["data"]);
    }
  }

  /// 添加评论
  addComment(NewsEvent event,Emitter<NewsState> emit) {
    Map<String,dynamic> params = {
      "createUserName": AppApplication.getInstance().userInfoEntity.userName,
      "questionId":state.model?.unitGuid,
      "commentContent": state.commentContent, //评论内容
      "picture": "2", //图片
      "type": state.type, //类型 1是评论 2是回复
      "commentId":state.type == 1 ? "" :state.commentModel?.unitGuid, // 评论id
      "parentId": state.type == 1 ? "" :state.commentModel?.createUser, //上级ID 回复的记录的主键
      "parentName": state.type == 1 ? "" : state.commentModel?.userName,
      "userType": 1,
      "category": 2,
      "likesNumber": 0
    };
    EasyLoading.show();
    ApiInstance().post("${StrUtil.parkModule}/sq/comment/add",data: params,errorFunction: (error) {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    }).then((response) {
      EasyLoading.dismiss();
      if (response['code'] == 200) {
        ToastUtil.showSuccessToast("评论发表成功");
        AppApplication.getCurrentState()?.pop();
        state.commentPageNumber = 1;
        add(CommentRefreshEvent());
      } else {
        ToastUtil.showErrorToast(response["msg"]??response["message"]??response["data"]);
      }
    });
  }

  /// 点赞
  clickLike(NewsEvent event,Emitter<NewsState> emit) {
    Map<String,dynamic> params = {
      "questionId": state.commentModel?.unitGuid,
      "type": "1",
      "category": 3,
      "userType": "1"
    };
    EasyLoading.dismiss();
    ApiInstance().post("${StrUtil.parkModule}/sq/likerecord/add",data: params,errorFunction: (error) {
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    }).then((response) {
      EasyLoading.dismiss();
      state.commentPageNumber = 1;
      add(CommentRefreshEvent());
    });
  }

  /// 评论弹窗
  showCommentDialog(NewsEvent event,Emitter<NewsState> emit) {
    showDialog(
      context: state.context!,
      builder: (BuildContext context) {
        return GestureDetector(
          onTap: (){
            AppApplication.getCurrentState()?.pop();
          },
          child: Material(
            color: Colors.transparent,
            child: AnimatedPadding(
              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
              duration: Duration(milliseconds: 200),
              curve: Curves.easeInOut,
              child: Column(
                children: [
                  const Spacer(),
                  CommentView(
                    releaseTap: (value) {
                      add(ReleaseCommentAndReply(value));
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 回复消息列表
  showReplyListDialog(NewsEvent event,Emitter<NewsState> emit) {
    replyController = RefreshController();
    showModalBottomSheet(
      context: state.context!,
      builder: (BuildContext context) {
        return ReplyView(
          bloc:  this,
          state: state,
          controller: replyController!,
        );
      },
    );
  }

  @override
  Future<void> close() {
    refreshController.dispose();
    replyController?.dispose();
    return super.close();
  }
}

class CommentView extends StatefulWidget {
  final Function releaseTap;
  const CommentView({super.key,required this.releaseTap});

  @override
  State<CommentView> createState() => _CommentViewState();
}

class _CommentViewState extends State<CommentView> {

  TextEditingController controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: AppTheme.white,
      child: Column(
        children: [
          TextFormField(
            controller: controller,
            inputFormatters: [
              FilteringTextInputFormatter.deny(RegExp(
                  "[^\\u0020-\\u007E\\u00A0-\\u00BE\\u2E80-\\uA4CF\\uF900-\\uFAFF\\uFE30-\\uFE4F\\uFF00-\\uFFEF\\u0080-\\u009F\\u2000-\\u201f\r\n]")),
              LengthLimitingTextInputFormatter(200)
            ],
            keyboardType: TextInputType.text,
            autofocus: true,
            decoration: InputDecoration(
                hintText: "请输入评论内容",
                hintStyle: TextStyle(
                  fontSize: 14.sp,
                  color: Color(0xFF999999),
                ),
                border: OutlineInputBorder(
                    borderSide: BorderSide(color: AppTheme.bgB, width: 1),
                    borderRadius: BorderRadius.all(Radius.circular(5.w)),
                    gapPadding: 0
                )
            ),
            maxLines: 3,
            minLines: 3,
          ),
          Row(
            children: [
              Spacer(),
              TextButton(onPressed:(){
                logger.d("发布的输入消息-----${controller.text}");
                if (controller.text.isNotEmpty) {

                  widget.releaseTap.call(controller.text);
                }
              }, child: Text("发布",style: TextStyle(
                  fontSize: 16.sp,
                  color: AppTheme.themeBlue,
                  fontWeight: FontWeight.w500
              ),))
            ],
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }
}

class ReplyView extends StatefulWidget {
  final NewsBloc bloc;
  final NewsState state;
  final RefreshController controller;
  const ReplyView({super.key,required this.bloc,required this.state, required this.controller,});

  @override
  State<ReplyView> createState() => _ReplyViewState();
}

class _ReplyViewState extends State<ReplyView> {

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.6,
        minHeight: MediaQuery.of(context).size.height * 0.2,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(15),
          topRight: Radius.circular(15)
        )
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Center(child: Text("发布评论", style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),)),
          ),
          Expanded(
            child: NestedScrollView(
              headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled){
                return [
                  SliverToBoxAdapter(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CircleAvatar(
                              radius: 18,
                              backgroundImage: CachedNetworkImageProvider(
                                widget.state.commentModel?.headIcon??'',
                              ),
                            ),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.state.commentModel?.userName??'',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 5),
                                  Text(
                                    widget.state.commentModel?.commentContent??'',
                                    style: const TextStyle(fontSize: 15),
                                  ),
                                  const SizedBox(height: 5),
                                  Text(
                                    widget.state.commentModel?.createDate??'',
                                    style: const TextStyle(
                                      fontSize: 13,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        // const SizedBox(height: 6),
                        // Row(
                        //   mainAxisAlignment: MainAxisAlignment.end,
                        //   children: [
                        //     GestureDetector(
                        //       onTap:(){
                        //         widget.bloc.add(AddCommentEvent(2));
                        //       },
                        //       child: Row(
                        //         mainAxisSize: MainAxisSize.min,
                        //         children: [
                        //           Icon(
                        //             Icons.mode_comment_outlined,
                        //             color: Colors.grey,
                        //             size: 20,
                        //           ),
                        //           const SizedBox(width: 2),
                        //           Text("${widget.state.commentModel?.number ??''}", style: TextStyle(color: Colors.grey)),
                        //         ],
                        //       ),
                        //     ),
                        //     const SizedBox(width: 16),
                        //     GestureDetector(
                        //       onTap: (){
                        //        widget.bloc.add(LikeTapEvent());
                        //       },
                        //       child: Row(
                        //           mainAxisSize: MainAxisSize.min,
                        //           children: [
                        //             Icon(
                        //               Icons.thumb_up_alt_outlined,
                        //               color: Colors.grey,
                        //               size: 20,
                        //             ),
                        //             const SizedBox(width: 2),
                        //             Text(
                        //               '${widget.state.commentModel?.likesNumber??0}',
                        //               style: TextStyle(color: Colors.grey),
                        //             ),
                        //           ]
                        //       ),
                        //     )
                        //   ],
                        // ),
                        const SizedBox(height: 8),
                        Container(height: 1, color: Color(0xFFF0F0F0)),
                      ],
                    ),
                  ),
                  SliverPersistentHeader(delegate: _replySliverPersistentHeader(
                      maxHeight: 60,
                      minHeight: 60,
                      child: Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Row(children: [
                          Text("全部回复",style: TextStyle(fontSize: 16,fontWeight: FontWeight.bold),),
                          Spacer()
                        ]),
                      )),pinned:  true,)
                ];
              },
              body: SmartRefresher(
                header: WaterDropHeader(),
                  footer: ClassicFooter(),
                  controller: widget.controller,
                  enablePullUp: true,
                  enablePullDown:  true,
                  onLoading: () async {
                    widget.bloc.add(ReplyLoadMoreEvent());
                  },
                  onRefresh: () async {
                   widget.bloc.add(ReplyRefreshEvent());
                  },
                  child: ListView.builder(
                      itemCount: widget.state.replyList.length,
                      itemBuilder: (BuildContext context, int index) {
                        CommentModel model = widget.state.replyList[index];
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CircleAvatar(
                                  radius: 18,
                                  backgroundImage: CachedNetworkImageProvider(
                                    model.headIcon??'',
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        model.userName??'',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 5),
                                      Text(
                                        "回复${widget.state.commentModel?.userName}:${model.commentContent??''}",
                                        style: const TextStyle(fontSize: 15),
                                      ),
                                      const SizedBox(height: 5),
                                      Text(
                                        model.createDate??'',
                                        style: const TextStyle(
                                          fontSize: 13,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 6),
                            Container(height: 1, color: Color(0xFFF0F0F0)),
                          ],
                        );
                      }
                  )
              ),
            ),
          ),
        ],
      ),
    );
  }
  @override
  void dispose() {
    widget.controller.dispose();
    super.dispose();
  }
}

class _replySliverPersistentHeader extends SliverPersistentHeaderDelegate {
  final double maxHeight;
  final double minHeight;
  final Widget child;
 const _replySliverPersistentHeader({
    required this.maxHeight,
    required this.minHeight,
    required this.child,
  });


  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(_replySliverPersistentHeader oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}



