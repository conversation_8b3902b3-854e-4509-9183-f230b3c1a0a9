import 'package:bloc_test/models/CommentModel.dart';
import 'package:bloc_test/models/IndustryNewsModel.dart';
import 'package:flutter/cupertino.dart';

class NewsState {
  IndustryNewsModel? model;
  int commentPageNumber = 1;
  int pageSize = 10;
  int replyPageNumber = 1;
  List<CommentModel> commentList = [];
  List replyList =  [];
  String commentContent = "";
  int type = 2;//类型 1是评论 2是回复
  CommentModel? commentModel;
  BuildContext? context;
  NewsState init() {
    return NewsState()
    ..pageSize = 10
      ..commentPageNumber = 1
      ..replyPageNumber = 1
      ..commentList = []
      ..replyList = []
      ..commentContent = ""
      ..type = 2
      ..commentModel = null
      ..context = null
    ..model = null;
  }

  NewsState clone() {
    return NewsState()
    ..pageSize = pageSize
      ..commentPageNumber = commentPageNumber
      ..replyPageNumber = replyPageNumber
      ..commentList = commentList
      ..replyList = replyList
      ..commentContent = commentContent
      ..type = type
      ..commentModel = commentModel
      ..context = context
    ..model = model;
  }
}
