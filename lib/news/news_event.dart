import 'package:bloc_test/models/IndustryNewsModel.dart';
import 'package:flutter/cupertino.dart';

abstract class NewsEvent {}

class InitEvent extends NewsEvent {
  final IndustryNewsModel model;
  final BuildContext context;
  InitEvent(this.model,this.context);
}
class AddCommentEvent extends NewsEvent {
  final int type;
  AddCommentEvent(this.type);
}

class LikeTapEvent extends NewsEvent {
  LikeTapEvent();
}

class CommentRefreshEvent extends NewsEvent {
  CommentRefreshEvent();
}

class CommentLoadMoreEvent extends NewsEvent {
  CommentLoadMoreEvent();
}

class ReplyRefreshEvent extends NewsEvent {
  ReplyRefreshEvent();
}

class ReplyLoadMoreEvent extends NewsEvent {
  ReplyLoadMoreEvent();
}
class ReleaseCommentAndReply extends NewsEvent {
  final String commentContent;
  ReleaseCommentAndReply(this.commentContent);
}

class GetReplyListEvent extends NewsEvent {
  GetReplyListEvent();
}