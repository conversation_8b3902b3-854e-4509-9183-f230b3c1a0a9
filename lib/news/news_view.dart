import 'package:bloc_test/models/IndustryNewsModel.dart';
import 'package:bloc_test/news/news_state.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'news_bloc.dart';
import 'news_event.dart';

class NewsPage extends StatelessWidget {
  final IndustryNewsModel model;
  const NewsPage({super.key,required this.model});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => NewsBloc()..add(InitEvent(model,context)),
      child: _buildPage(context),
    );
  }

  Widget _buildPage(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(title: Text('详情')),
      body: BlocBuilder<NewsBloc, NewsState>(
        builder: (context, state) {
          final block = BlocProvider.of<NewsBloc>(context);
          state.context = context;
          return NestedScrollView(
            physics: const BouncingScrollPhysics(),
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                // 正文内容
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 12.h),
                        // 新闻信息
                        Row(
                          children: [
                            Text(
                              model.notarialName ?? "",
                              style: TextStyle(fontSize: 14.sp, color: Colors.black54),
                            ),
                            const Spacer(),
                            Text(
                              model.createDate ?? '',
                              style: TextStyle(fontSize: 13.sp, color: Colors.grey),
                            ),
                          ],
                        ),
                        SizedBox(height: 16.h),
                        // WebView 容器

                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child:  SizedBox(
                    width: double.infinity,
                    child: Html(data: model.content??''),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Container(
                    height: 10,
                    color: Colors.grey.withAlpha(100),
                  ),
                ),
                SliverPersistentHeader(delegate: _SliverHeaderDelegate(minHeight: 60, maxHeight: 60, child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                      color: AppTheme.white,
                      border: Border(
                        bottom: BorderSide(
                          color: AppTheme.bgB,
                          width: 0.5,
                        ),
                      )
                  ),
                  child: Row(
                    children: [
                      Text(
                        '评论 ${state.commentList.length}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Spacer(),
                    ],
                  ),
                )),pinned:  true,),
              ];
            },
            body: Column(
              children: [
                Expanded(child: SmartRefresher(
                  enablePullDown: true,
                  enablePullUp: true,
                  onRefresh:()async{
                    block.add(CommentRefreshEvent());
                  },
                  onLoading: (){
                    block.add(CommentLoadMoreEvent());
                  },
                  controller: block.refreshController,
                  child: ListView.builder(
                      itemCount: state.commentList.length,
                      itemBuilder: (context,index){
                        final c = state.commentList[index];
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CircleAvatar(
                                    radius: 18,
                                    backgroundImage: CachedNetworkImageProvider(
                                      c.headIcon??'',
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          c.userName??'',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                          c.commentContent??'',
                                          style: const TextStyle(fontSize: 15),
                                        ),
                                        GestureDetector(
                                          onTap: c.number != null && c.number != 0 ? (){
                                            state.commentModel = c;
                                            block.add(GetReplyListEvent());
                                          }:null,
                                          child: Padding(
                                            padding: const EdgeInsets.only(
                                              top: 6,
                                              bottom: 2,
                                            ),
                                            child: Container(
                                              width: double.infinity,
                                              padding: const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 6,
                                              ),
                                              decoration: BoxDecoration(
                                                color: Color(0xFFF5F7FA),
                                                borderRadius: BorderRadius.circular(
                                                  6,
                                                ),
                                              ),
                                              child: Text(
                                                c.number != null && c.number != 0
                                                    ? '共${c.number}条回复信息 >'
                                                    : "暂无回复",
                                                style: const TextStyle(
                                                  color: Color(0xFF3CA0FF),
                                                  fontSize: 14,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                        Text(
                                          c.createDate??'',
                                          style: const TextStyle(
                                            fontSize: 13,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 6),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  GestureDetector(
                                    onTap: (){
                                      state.commentModel = c;
                                      block.add(AddCommentEvent(2));
                                    },
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.mode_comment_outlined,
                                          color: Colors.grey,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 2),
                                        Text("${c.number ??''}", style: TextStyle(color: Colors.grey)),
                                      ]
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  GestureDetector(
                                    onTap: (){
                                      block.add(LikeTapEvent());
                                    },
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          Icons.thumb_up_alt_outlined,
                                          color: Colors.grey,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 2),
                                        Text(
                                          '${c.likesNumber??0}',
                                          style: TextStyle(color: Colors.grey),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              ),
                              const SizedBox(height: 8),
                              Container(height: 1, color: Color(0xFFF0F0F0)),
                            ],
                          ),
                        );
                      }),)),
                Container(
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                  height: 50.h + MediaQuery.of(context).padding.bottom,
                  alignment: Alignment.center,
                  child: IconButton(onPressed: (){
                    block.add(AddCommentEvent(1));
                  }, icon: Icon(Icons.messenger,size: 20,)),
                )
              ],
            )
          );
        },
      ),
    );
  }
}

class _SliverHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minHeight;
  final double maxHeight;
  final Widget child;
  _SliverHeaderDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });


  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => maxHeight;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverHeaderDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
