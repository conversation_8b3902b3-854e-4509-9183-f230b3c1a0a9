import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';

import '../utils/block_puzzle_captcha.dart';
import '../utils/event_bus_instance.dart';
import 'binding_email_account_event.dart';
import 'binding_email_account_state.dart';

class BindingEmailAccountBloc extends Bloc<BindingEmailAccountEvent, BindingEmailAccountState> {
  Timer? _countdownTimer;
  
  BindingEmailAccountBloc() : super(BindingEmailAccountState().init()) {
    on<InitEvent>(_init);
    on<SendVerificationCodeEvent>(_sendVerificationCode);
    on<SubmitBindingEvent>(_submitBinding);
    on<UpdateEmailEvent>(_updateEmail);
    on<UpdateVerificationCodeEvent>(_updateVerificationCode);
    on<TimerCountEvent>(_timerCountEvent);
    on<ResetSubmitStateEvent>(_resetSubmitStateEvent);
    on<ResetVerificationCodeEvent>(_resetVerificationCodeEvent);
    on<ErrorMessageEvent>(_errorMessageEvent);
    on<TimerCountIncreaseAndDecreaseEvent>(_timerCountIncreaseAndDecreaseEvent);
  }

  @override
  Future<void> close() {
    _countdownTimer?.cancel();
    return super.close();
  }

  void _init(InitEvent event, Emitter<BindingEmailAccountState> emit) async {
    emit(state.clone()..phoneNumber = event.phoneNumber);
  }

  void _updateEmail(UpdateEmailEvent event, Emitter<BindingEmailAccountState> emit) {
    emit(state.clone()..email = event.email..errorMessage = null);
  }

  void _updateVerificationCode(UpdateVerificationCodeEvent event, Emitter<BindingEmailAccountState> emit) {
    emit(state.clone()..verificationCode = event.code..errorMessage = null);
  }

  void _sendVerificationCode(SendVerificationCodeEvent event, Emitter<BindingEmailAccountState> emit) async {
    if (!RegexUtil.isEmail(event.email)) {
      emit(state.clone()..errorMessage = "请输入正确的邮箱格式");
      return;
    }
    emit(state.clone()..isSendingCode = true..errorMessage = null);
    getImgCode(emit);
  }

  void _submitBinding(SubmitBindingEvent event, Emitter<BindingEmailAccountState> emit) async {
    if(state.email.isEmpty){
      emit(state.clone()..errorMessage = "请输入邮箱");
      return;
    }
    if(!RegexUtil.isEmail(state.email)){
      emit(state.clone()..errorMessage = "请输入正确的邮箱格式");
      return;
    }
    if(state.verificationCode.isEmpty){
      emit(state.clone()..errorMessage = "请输入验证码");
      return;
    }
    if(state.verificationCode.length != 6){
      emit(state.clone()..errorMessage = "验证码长度为6位");
      return;
    }
    emit(state.clone()..isSubmitting = true..errorMessage = null);
    bindEmail(emit);
  }

  void _resetSubmitStateEvent(ResetSubmitStateEvent event, Emitter<BindingEmailAccountState> emit) {
    emit(state.clone()..isSubmitting = false..errorMessage = null);
  }

  void _errorMessageEvent(ErrorMessageEvent event, Emitter<BindingEmailAccountState> emit) {
    emit(state.clone()..errorMessage = event.errorMessage);
  }

  void _resetVerificationCodeEvent(ResetVerificationCodeEvent event, Emitter<BindingEmailAccountState> emit) {
    emit(state.clone()..isSendingCode = false..canSendCode = true);
  }
  void _timerCountEvent(TimerCountEvent event, Emitter<BindingEmailAccountState> emit) {
    emit(state.clone()..canSendCode = false..isSendingCode = false..countdown = 120);

    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.countdown > 0) {
        add(TimerCountIncreaseAndDecreaseEvent(state.countdown-1));
      } else {
        timer.cancel();
        add(TimerCountIncreaseAndDecreaseEvent(0));
        add(ResetVerificationCodeEvent(false, true));
      }
    });
  }
  void _timerCountIncreaseAndDecreaseEvent(TimerCountIncreaseAndDecreaseEvent event, Emitter<BindingEmailAccountState> emit) {
    emit(state.clone()..countdown = event.count);
  }
  void getImgCode(Emitter<BindingEmailAccountState> emit) {
    showDialog<Null>(
      context: AppApplication.currentContext!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BlockPuzzleCaptchaPage(
          onSuccess: (v, token) {
            getEmailCode(v, token,emit);
          },
          onFail: () {

          },
          close: () {
            if (_countdownTimer==null) {
              add(ResetVerificationCodeEvent(false, true));
            }
          },
        );
      },
    );
  }

  getEmailCode(String pointJson,String token,Emitter<BindingEmailAccountState> emit) async {
    Map<String, dynamic> map = {
      'email':state.email,
      "pointJson":pointJson,
      "token":token
    };
    final response = await ApiInstance().post(
        '${StrUtil.userModule}/cgz/userApp/sendEmailCode',
      data: map,
      errorFunction: (error) {
        ToastUtil.showErrorToast(StrUtil.httpError);
        add(ResetVerificationCodeEvent(false, true));
      }
    );
    if (response != null) {
      if (response["code"] ==200) {
        ToastUtil.showSuccessToast("验证码获取成功！");
        AppApplication.pop();
        add(TimerCountEvent());
      } else if (response["code"] == 12003) {
        eventBus.fire(EventBusInstanceEvent(source: 'checkFail'));
        add(ResetVerificationCodeEvent(false, true));
      } else {
        add(ErrorMessageEvent(response['data'] ?? response['message']));
        add(ResetVerificationCodeEvent(false, true));
      }
    } else {
      add(ErrorMessageEvent(response['data'] ?? response['message']));
      add(ResetVerificationCodeEvent(false, true));
    }
  }
  
  /// 绑定邮箱
  bindEmail(Emitter<BindingEmailAccountState> emit)async {
    Map<String,dynamic> map = {
      'email':state.email,
      'emailCode':state.verificationCode,
      'mobile':state.phoneNumber
    };
    final response = await ApiInstance().post(
        "${StrUtil.userModule}/cgz/userApp/doSetEmail",
      data: map,
      errorFunction: (error) {
          add(ResetSubmitStateEvent(false));
          add(ErrorMessageEvent(StrUtil.httpError));
      }
    );
    if(response!=null){
      if(response['code']!=200){
        ToastUtil.showErrorToast(response['message']);
        add(ResetSubmitStateEvent(false));
        add(ErrorMessageEvent(response["message"]));
        return;
      }
      add(ResetSubmitStateEvent(false));
      add(ErrorMessageEvent("绑定邮箱成功"));
      AppApplication.pop();
    }else{
      add(ResetSubmitStateEvent(false));
      add(ErrorMessageEvent("绑定邮箱失败"));
    }
  }

}
