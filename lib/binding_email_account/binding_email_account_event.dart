abstract class BindingEmailAccountEvent {}

class InitEvent extends BindingEmailAccountEvent {
  final String phoneNumber;
  InitEvent(this.phoneNumber);
}

class SendVerificationCodeEvent extends BindingEmailAccountEvent {
  final String email;
  SendVerificationCodeEvent(this.email);
}

class SubmitBindingEvent extends BindingEmailAccountEvent {
  final String email;
  final String verificationCode;
  SubmitBindingEvent({required this.email, required this.verificationCode});
}

class UpdateEmailEvent extends BindingEmailAccountEvent {
  final String email;
  UpdateEmailEvent(this.email);
}

class UpdateVerificationCodeEvent extends BindingEmailAccountEvent {
  final String code;
  UpdateVerificationCodeEvent(this.code);
}

class TimerCountEvent extends BindingEmailAccountEvent {}

class ResetSubmitStateEvent extends BindingEmailAccountEvent {
  final bool isSubmitting;
  ResetSubmitStateEvent(this.isSubmitting,);
}

class ResetVerificationCodeEvent extends BindingEmailA<PERSON>untEvent {
  final bool isSendingCode;
  final bool canSendCode;
  ResetVerificationCodeEvent(this.isSendingCode, this.canSendCode);
}

class ErrorMessageEvent extends BindingEmailAccountEvent {
  final String errorMessage;
  ErrorMessageEvent(this.errorMessage);
}

class TimerCountIncreaseAndDecreaseEvent extends BindingEmailAccountEvent {
  final int count;
  TimerCountIncreaseAndDecreaseEvent(this.count);
}