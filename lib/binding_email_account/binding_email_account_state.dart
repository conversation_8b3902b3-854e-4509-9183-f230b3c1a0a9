class BindingEmailAccountState {
  String? phoneNumber;
  String email = '';
  String verificationCode = '';
  bool isSendingCode = false;
  bool isSubmitting = false;
  bool canSendCode = true;
  int countdown = 120;
  String? errorMessage;
  
  BindingEmailAccountState init() {
    return BindingEmailAccountState()
    ..phoneNumber = null
    ..email = ''
    ..verificationCode = ''
    ..isSendingCode = false
    ..isSubmitting = false
    ..canSendCode = true
    ..countdown = 0
    ..errorMessage = null;
  }

  BindingEmailAccountState clone() {
    return BindingEmailAccountState()
    ..phoneNumber = phoneNumber
    ..email = email
    ..verificationCode = verificationCode
    ..isSendingCode = isSendingCode
    ..isSubmitting = isSubmitting
    ..canSendCode = canSendCode
    ..countdown = countdown
    ..errorMessage = errorMessage;
  }
}
