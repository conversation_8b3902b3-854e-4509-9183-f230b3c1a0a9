import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:bloc_test/utils/toast_util.dart';

import 'binding_email_account_bloc.dart';
import 'binding_email_account_event.dart';
import 'binding_email_account_state.dart';

class BindingEmailAccountPage extends StatefulWidget {
  final String phoneNumber;
  const BindingEmailAccountPage({super.key, required this.phoneNumber});

  @override
  State<BindingEmailAccountPage> createState() => _BindingEmailAccountPageState();
}

class _BindingEmailAccountPageState extends State<BindingEmailAccountPage> {
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _verificationCodeController = TextEditingController();
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _codeFocusNode = FocusNode();

  @override
  void dispose() {
    _emailController.dispose();
    _verificationCodeController.dispose();
    _emailFocusNode.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => BindingEmailAccountBloc()..add(InitEvent(widget.phoneNumber)),
      child: BlocListener<BindingEmailAccountBloc, BindingEmailAccountState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ToastUtil.showErrorToast(state.errorMessage!);
          }
        },
        child: BlocBuilder<BindingEmailAccountBloc, BindingEmailAccountState>(
          builder: (context, state) {
            final block = context.read<BindingEmailAccountBloc>();
            return Scaffold(
              backgroundColor: Colors.white,
              appBar: AppBar(
                title: const Text(
                  '绑定邮箱',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    // 顶部插图区域
                    _buildIllustrationSection(),
                    
                    // 输入表单区域
                    _buildInputSection(state,block),
                    
                    // 确认按钮
                    _buildConfirmButton(state,block),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildIllustrationSection() {
    return Container(
      width: double.infinity,
      height: 280,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.themeBlue,
            AppTheme.themeBlue.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // 背景装饰元素
          Positioned(
            top: 20,
            left: 20,
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          Positioned(
            top: 40,
            right: 30,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
            ),
          ),
          
          // 主要插图
          Center(
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 2,
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.email_outlined,
                    size: 60,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '邮箱绑定',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '绑定邮箱，提升账号安全性',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection(BindingEmailAccountState state,BindingEmailAccountBloc block) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 邮箱输入框
          _buildEmailInput(state,block),
          
          const SizedBox(height: 24),
          
          // 验证码输入框
          _buildVerificationCodeInput(state,block),
        ],
      ),
    );
  }

  Widget _buildEmailInput(BindingEmailAccountState state,BindingEmailAccountBloc block) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '邮箱',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: _emailFocusNode.hasFocus ? AppTheme.themeBlue : Colors.grey.shade300,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextField(
            controller: _emailController,
            focusNode: _emailFocusNode,
            keyboardType: TextInputType.emailAddress,
            decoration: InputDecoration(
              hintText: '请输入邮箱',
              hintStyle: TextStyle(
                color: Colors.grey.shade400,
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              block.add(UpdateEmailEvent(value));
            },
          ),
        ),
      ],
    );
  }

  Widget _buildVerificationCodeInput(BindingEmailAccountState state,BindingEmailAccountBloc block) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              '*',
              style: TextStyle(
                color: Colors.red,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '验证码',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: _codeFocusNode.hasFocus ? AppTheme.themeBlue : Colors.grey.shade300,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: TextField(
                  controller: _verificationCodeController,
                  focusNode: _codeFocusNode,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: '请输入邮箱验证码',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 14,
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onChanged: (value) {
                    block.add(UpdateVerificationCodeEvent(value));
                  },
                ),
              ),
            ),
            const SizedBox(width: 12),
            SizedBox(
              width: 120,
              height: 48,
              child: ElevatedButton(
                onPressed: state.canSendCode && !state.isSendingCode
                    ? () =>  block.add(SendVerificationCodeEvent(state.email))
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: state.canSendCode ? AppTheme.themeBlue : Colors.grey.shade300,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: state.isSendingCode
                    ? SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        state.canSendCode ? '获取验证码' : '${state.countdown}s',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildConfirmButton(BindingEmailAccountState state,BindingEmailAccountBloc block) {
    return Container(
      padding: const EdgeInsets.all(24.0),
      child: SizedBox(
        width: double.infinity,
        height: 48,
        child: ElevatedButton(
          onPressed: state.isSubmitting ? null : () => block.add(
            SubmitBindingEvent(
              email: state.email,
              verificationCode: state.verificationCode,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.themeBlue,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: state.isSubmitting
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(
                  '确认',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
        ),
      ),
    );
  }
}

