import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:url_launcher/url_launcher.dart';

import 'notary_list_bloc.dart';
import 'notary_list_event.dart';
import 'notary_list_state.dart';

class NotaryListPage extends StatelessWidget {
  const NotaryListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => NotaryListBloc()..add(InitEvent()),
      child: _buildPage(context),
    );
  }

  Widget _buildPage(BuildContext context) {
    return BlocBuilder<NotaryListBloc, NotaryListState>(
      builder: (context, state) {
        final block = BlocProvider.of<NotaryListBloc>(context);
        return Scaffold(
          appBar: AppBar(
            title: const Text('公证处'),
            actions: [
              IconButton(
                icon: const Icon(Icons.search, color: Colors.white),
                onPressed: () {
                  // 搜索功能
                },
              ),
            ],
            bottom: PreferredSize(
              preferredSize: Size.fromHeight(60),
              child: Container(
                color: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 10),
                alignment: Alignment.center,
                width: double.infinity,
                child: Row(
                  spacing: 30,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () {
                        // 切换Tab
                        block.add(TabChangeIndex(0));
                      },
                      child: Text(
                        '公证处',
                        style: state.tabIndex == 0 ? TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                        ) : TextStyle(color: Colors.grey, fontSize: 16.sp),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        block.add(TabChangeIndex(1));
                      },
                      child: Text(
                        '公证事项',
                        style: state.tabIndex == 1 ? TextStyle(
                          color: Colors.blue,
                          fontWeight: FontWeight.bold,
                          fontSize: 16.sp,
                        ) : TextStyle(color: Colors.grey, fontSize: 16.sp),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          body: BlocBuilder<NotaryListBloc, NotaryListState>(
            builder: (context, state) {
              if (state.offices.isEmpty) {
                return Center(child: Text('暂无数据'));
              }
              return SmartRefresher(
                controller: block.refreshController,
                enablePullUp: true,
                onRefresh: () async {
                  state.pageSize = 1;
                  block.add(RefreshEvent(state.pageSize));
                },
                enablePullDown: true,
                onLoading: () async {
                  state.pageSize += 1;
                  block.add(RefreshEvent(state.pageSize));
                },
                child: ListView.separated(
                  itemCount: state.offices.length,
                  separatorBuilder: (_, __) => Divider(height: 1),
                  itemBuilder: (context, index) {
                    final office = state.offices[index];
                    return ListTile(
                      leading: Image.asset(
                        'images/公证处_bg.png', // 替换为你的红色印章图片
                        width: 60,
                        height: 60,
                        fit: BoxFit.cover,
                      ),
                      title: Text(
                        office.name,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.location_on,
                                size: 16,
                                color: Colors.grey,
                              ),
                              SizedBox(width: 2),
                              Expanded(
                                child: Text(
                                  office.address,
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.black87,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 2),
                          Text(
                            '距离你${office.distance.toStringAsFixed(3)}km',
                            style: TextStyle(fontSize: 12, color: Colors.grey),
                          ),
                        ],
                      ),
                      trailing: IconButton(
                        icon: Icon(Icons.phone, color: Colors.blue),
                        onPressed: () {
                          // 拨打电话
                          canLaunchUrl(Uri.parse('tel:${office.phone}'));
                        },
                      ),
                      onTap: () {
                        // 可跳转详情
                      },
                    );
                  },
                ),
              );
            },
          ),
        );
      },
    );
  }
}
