import 'package:bloc/bloc.dart';
import 'package:bloc_test/models/NotaryOfficeModel.dart';
import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/amap_location_utils.dart';
import 'package:bloc_test/utils/toast_util.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../utils/common_tools.dart';
import 'notary_list_event.dart';
import 'notary_list_state.dart';

class NotaryListBloc extends Bloc<NotaryListEvent, NotaryListState> {
  
  RefreshController refreshController = RefreshController();
  
  NotaryListBloc() : super(NotaryListState().init()) {
    on<InitEvent>(_init);
    on<TabChangeIndex>(_tabChangeIndex);
    on<RefreshEvent>(_refreshEvent);
  }

  void _refreshEvent(RefreshEvent event, Emitter<NotaryListState> emit) async {
    requestLocationPermission(emit, (result) {
      emit(state.clone()..pageNum = event.pageIndex);
      final params = {
        "currentPage": state.pageNum,
        "pageSize": state.pageSize,
        "institutionType": "1",
        "distance": "${result['longitude']},${result["latitude"]}",
      };
      getNotaryList(params, emit);
    });

  }

  void _tabChangeIndex(TabChangeIndex event, Emitter<NotaryListState> emit) async {
    emit(state.clone()..tabIndex = event.index);
    requestLocationPermission(emit,(result){
      emit(state.clone()..pageNum = 1);
      final params = {
        "currentPage": state.pageNum,
        "pageSize": state.pageSize,
        "institutionType": "1",
        "distance": "${result['longitude']},${result["latitude"]}",
      };
      getNotaryList(params, emit);
    });
  }

  void _init(InitEvent event, Emitter<NotaryListState> emit) async {
    requestLocationPermission(emit,(result){
      emit(state.clone()..pageNum = 1);
      final params = {
        "currentPage": state.pageNum,
        "pageSize": state.pageSize,
        "institutionType": "1",
        "distance": "${result['longitude']},${result["latitude"]}",
      };
      getNotaryList(params, emit);
    });
  }

  // 定位权限请求
  Future<void> requestLocationPermission(Emitter<NotaryListState> emit,Function resultCallBack) async {
    if (!await Permission.location.status.isGranted) {
      CommonTools.showCustomToast(context: AppApplication.currentContext!, titleText: "定位权限使用说明：", subTitleText: "用于获取当前位置信息", time: 2);
    }
    final status = await Permission.location.request();
    if (status == PermissionStatus.granted) {
      AmapLocationUtils.getStart((result){
        resultCallBack.call();
      },errorCallBack: (){
        ToastUtil.showErrorToast("获取定位信息失败！");
      });
    } else {
      CommonTools.showPermissionDialog(str: "访问定位权限");
    }
  }

  
  // 网络请求
  getNotaryList(Map<String,dynamic>params, Emitter<NotaryListState> emit) async {
    // 模拟网络请求
    EasyLoading.show();
   final data = await ApiInstance().post("${StrUtil.notaryModule}/cgz/notarialOffice/selectPage",queryParameters: params,errorFunction: (error){
      EasyLoading.dismiss();
      ToastUtil.showErrorToast(StrUtil.httpError);
    });

   if (data != null && data["code"] == 200) {
     EasyLoading.dismiss();
     if (state.pageSize == 1){
       emit(state.clone()..offices = []);
     }
     if (data["items"] !=null && data["items"].length > 0) {
       List<NotaryOfficeModel> tempList = [];
        data["items"].forEach((element) {
          NotaryOfficeModel model = NotaryOfficeModel.fromJson(element);
          tempList.add(model);
        });
        if (state.pageSize == 1){
          emit(state.clone()..offices = tempList);
        } else {
          emit(state.clone()..offices = [...state.offices, ...tempList]);
        }

     } else {
       state.offices = [];
     }
   } else {
     EasyLoading.dismiss();
   }
  }
}
