import 'package:bloc_test/models/NotaryOfficeModel.dart';

class NotaryListState {
  late List<NotaryOfficeModel> offices;
  int tabIndex = 0;

  int pageSize = 10;
  int pageNum = 1;
  NotaryListState init() {
    return NotaryListState()
    ..offices = []
      ..pageSize = 10
      ..pageNum = 1
    ..tabIndex = 0;
  }

  NotaryListState clone() {
    return NotaryListState()
    ..offices = offices
      ..pageSize = pageSize
      ..pageNum = pageNum
    ..tabIndex = tabIndex;
  }
}
