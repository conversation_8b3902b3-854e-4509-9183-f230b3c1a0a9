import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import 'webview_page_bloc.dart';
import 'webview_page_event.dart';
import 'webview_page_state.dart';

class WebViewPagePage extends StatelessWidget {
  final String urlString;
  final String titleString;
  const WebViewPagePage({super.key, required this.urlString, required this.titleString});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => WebviewPageBloc()..add(InitEvent()),
      child: _buildPage(context),
    );
  }

  Widget _buildPage(BuildContext context) {
    return BlocBuilder<WebviewPageBloc, WebviewPageState>(builder: (context,state){
      final bloc = BlocProvider.of<WebviewPageBloc>(context);
      return Scaffold(
        appBar: AppBar(
          title: Text(titleString),
        ),
        body: Stack(
          children: [
            InAppWebView(
              initialSettings: bloc.settings,
              onWebViewCreated: (InAppWebViewController controller) {
                bloc.webViewController = controller;
              },
              onLoadStop: (InAppWebViewController controller, Uri? url) async {
                bloc.add(WebViewPageFinishedEvent(false));
              },
              initialUrlRequest: URLRequest(url: WebUri(urlString)),
            ),
            Offstage(
              offstage: !state.isLoading,
              child: Center(
                child: CircularProgressIndicator(),
              ),
            )
          ],
        )
      );
    });
  }
}

