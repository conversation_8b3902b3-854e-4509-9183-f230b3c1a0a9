import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

import 'webview_page_event.dart';
import 'webview_page_state.dart';

class WebviewPageBloc extends Bloc<WebviewPageEvent, WebviewPageState> {

  InAppWebViewController? webViewController;
  InAppWebViewSettings settings = InAppWebViewSettings(
      isInspectable: kDebugMode,
      mediaPlaybackRequiresUserGesture: false,
      allowsInlineMediaPlayback: true,
      iframeAllow: "camera; microphone",
      iframeAllowFullscreen: true);

  WebviewPageBloc() : super(WebviewPageState().init()) {
    on<InitEvent>(_init);
    on<WebViewPageFinishedEvent>(_webViewPageFinished);
  }

  void _webViewPageFinished(WebViewPageFinishedEvent event, Emitter<WebviewPageState> emit) async {
    emit(state.clone()..isLoading = event.isLoading);
  }

  void _init(InitEvent event, Emitter<WebviewPageState> emit) async {
    emit(state.clone());
  }
}
