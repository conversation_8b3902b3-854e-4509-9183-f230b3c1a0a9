import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'video_conference_bloc.dart';
import 'video_conference_event.dart';

class VideoConferencePage extends StatelessWidget {
  const VideoConferencePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => VideoConferenceBloc()..add(InitEvent()),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final bloc = BlocProvider.of<VideoConferenceBloc>(context);

    return Container();
  }
}

