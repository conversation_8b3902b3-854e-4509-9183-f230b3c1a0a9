import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
class DianziNamePage extends StatelessWidget {
  const DianziNamePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("电子签名服务告知条款")),
      body: SafeArea(child: Padding(padding: EdgeInsets.all(20.w),
      child: Text(
          """   当您选择开始公证的时候，您同意采用电子签名的方式，签署公证过程中所形成的法律文书，并同意由您或授权他人向江苏智慧数字认证申请电子签名认证证书，自愿遵守其电子认证业务规则。您保证提交的申请资料真实、准确、完整，愿意承担由于资料虚假失实而导致的一切后果。"""),)),
    );
  }
}

