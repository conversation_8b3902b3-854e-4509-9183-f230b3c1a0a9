import 'package:flutter/material.dart';

import '../utils/appTheme.dart';

class GuideDetailPage extends StatelessWidget {
   final int arguments;
  const GuideDetailPage({super.key,required this.arguments});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("办证指南")),
      backgroundColor: AppTheme.chipBackground,
      body: SingleChildScrollView(
        child: Image.asset(
          arguments == 1 ? "images/zizhu_guide.png" : "images/video_guide.png",
          width: double.infinity,
          fit: BoxFit.fill,
        ),
      ),
    );
  }
}
