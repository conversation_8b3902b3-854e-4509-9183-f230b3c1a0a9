


import 'package:bloc_test/utils/ApiInstance.dart';
import 'package:bloc_test/utils/AppApplication.dart';
import 'package:bloc_test/utils/StrUtil.dart';
import 'package:bloc_test/utils/appTheme.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinyin/pinyin.dart';

import '../utils/toast_util.dart';

/// 城市选择弹窗
///
///
///
///
// 城市选择器
showCityPickerView(BuildContext context,
    {required Function resultCallBack, required String locationCode}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (ctx) {
      return ConstrainedBox(
        constraints: BoxConstraints(maxHeight: 400),
        child: Container(
          color: Colors.white,
          child: CityPickerView(
            locationCode: locationCode,
            onResult: (res) {
              resultCallBack(res);
            },
          ),
        ),
      );
    },
  );
}

// 城市选择
typedef ResultBlock = void Function(CityResult result);

class CityPickerView extends StatefulWidget {
  // 结果返回
  final ResultBlock onResult;
  final String locationCode;
  const CityPickerView({super.key, required this.onResult, required this.locationCode});
  @override
  _CityPickerViewState createState() => _CityPickerViewState();
}

class _CityPickerViewState extends State<CityPickerView> {
  List<ProvinceModelClass> datas = [];
  int? provinceIndex;
  int? cityIndex;
  int? areaIndex;

  FixedExtentScrollController? provinceScrollController;
  FixedExtentScrollController? cityScrollController;
  FixedExtentScrollController? areaScrollController;

  CityResult result = CityResult();

  bool isShow = false;

  List<ProvinceModelClass> get provinces {
    if (datas.isNotEmpty) {
      if (provinceIndex == null) {
        provinceIndex = 0;
        result.province = provinces[provinceIndex!].name;
        result.provinceCode = provinces[provinceIndex!].id.toString();
      }
      return datas;
    }
    return [];
  }

  List<CityModelClass> get citys {
    if (provinces.isNotEmpty) {
      return provinces[provinceIndex!].treeChild ?? [];
    }
    return [];
  }

  List<AreaModelClass> get areas {
    if (citys.isNotEmpty) {
      if (cityIndex == null) {
        cityIndex = 0;
        result.city = citys[cityIndex!].name;
        result.cityCode = citys[cityIndex!].id.toString();
      }
      List<AreaModelClass> list = citys[cityIndex!].treeChild ?? [];
      if (list.isNotEmpty) {
        if (areaIndex == null) {
          areaIndex = 0;
          result.area = list[areaIndex!].name;
          result.areaCode = list[areaIndex!].id.toString();
        }
      }
      return list;
    }
    return [];
  }

  // 保存选择结果
  _saveInfoData() {
    var prs = provinces;
    var cts = citys;
    var ars = areas;
    if (provinceIndex != null && prs.isNotEmpty) {
      result.province = prs[provinceIndex!].name;
      result.provinceCode = prs[provinceIndex!].id.toString();
    } else {
      result.province = '';
      result.provinceCode = '';
    }

    if (cityIndex != null && cts.isNotEmpty) {
      result.city = cts[cityIndex!].name;
      result.cityCode = cts[cityIndex!].id.toString();
    } else {
      result.city = '';
      result.cityCode = '';
    }

    if (areaIndex != null && ars.isNotEmpty) {
      result.area = ars[areaIndex!].name;
      result.areaCode = ars[areaIndex!].id.toString();
    } else {
      result.area = '';
      result.areaCode = '';
    }
  }

  @override
  void dispose() {
    provinceScrollController?.dispose();
    cityScrollController?.dispose();
    areaScrollController?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    //初始化控制器
    provinceScrollController = FixedExtentScrollController();
    cityScrollController = FixedExtentScrollController();
    areaScrollController = FixedExtentScrollController();

    //读取city.json数据
    _loadCity();
  }

  Future _loadCity() async {
    final response = await ApiInstance().get("${StrUtil.userModule}/sysregion/get/2",errorFunction: (error) {});
    if (response['code'] == 200) {
      if (response['data'] != null && response['data'].isNotEmpty) {
        try {
          datas = response['data']
              .map<ProvinceModelClass>((e) => ProvinceModelClass.fromMap(e))
              .toList();
          if (widget.locationCode.isNotEmpty) {
            for (var element in datas) {
              for (var element1 in element.treeChild!) {
                if (element1.id.toString() == widget.locationCode) {
                  cityIndex = element.treeChild?.indexOf(element1);
                  provinceIndex = datas.indexOf(element);
                  result.province = element.name;
                  result.provinceCode = element.id.toString();
                  result.city = element1.name;
                  result.cityCode = element1.id.toString();
                  cityScrollController =
                      FixedExtentScrollController(initialItem: cityIndex!);
                  provinceScrollController =
                      FixedExtentScrollController(initialItem: provinceIndex!);
                  setState(() {});
                }
              }
            }
          }
        } catch (e) {
          logger.d("报错信息：$e");
        }
      } else {
        datas = [];
      }
      setState(() {
        isShow = true;
      });
    } else {
      ToastUtil.showErrorToast("获取城市信息失败");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          _firstView(),
          _contentView(),
        ],
      ),
    );
  }

  Widget _firstView() {
    return Container(
      height: 44,
      decoration: BoxDecoration(
        border: Border(
            bottom: BorderSide(color: Colors.grey.withOpacity(0.1), width: 1)),
      ),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                AppApplication.pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () {
                widget.onResult(result);
                AppApplication.pop();
              },
            ),
          ]),
    );
  }

  Widget _contentView() {
    return SizedBox(
      // color: Colors.orange,
      height: 300,
      child: isShow
          ? Row(
        children: <Widget>[
          Expanded(child: _provincePickerView()),
          Expanded(child: _cityPickerView()),
          // Expanded(child: _areaPickerView()),
        ],
      )
          : Center(
        child: CupertinoActivityIndicator(
          animating: true,
        ),
      ),
    );
  }

  Widget _provincePickerView() {
    return CupertinoPicker(
      scrollController: provinceScrollController,
      onSelectedItemChanged: (index) {
        provinceIndex = index;
        if (cityIndex != null) {
          cityIndex = 0;
          if (cityScrollController!.positions.isNotEmpty) {
            cityScrollController!.jumpTo(0);
          }
        }
        if (areaIndex != null) {
          areaIndex = 0;
          if (areaScrollController!.positions.isNotEmpty) {
            areaScrollController!.jumpTo(0);
          }
        }
        _saveInfoData();
        setState(() {});
      },
      itemExtent: 36,
      children: provinces.map((item) {
        return Center(
          child: Text(
            item.name??'',
            style: TextStyle(color: Colors.black87, fontSize: 16),
            maxLines: 1,
          ),
        );
      }).toList(),
    );
  }

  Widget _cityPickerView() {
    return Container(
      child: citys.isEmpty
          ? Container()
          : CupertinoPicker(
        scrollController: cityScrollController,
        onSelectedItemChanged: (index) {
          cityIndex = index;
          if (areaIndex != null) {
            areaIndex = 0;
            if (areaScrollController!.positions.isNotEmpty) {
              areaScrollController!.jumpTo(0);
            }
          }
          _saveInfoData();
          setState(() {});
        },
        itemExtent: 36,
        children: citys.map((item) {
          return Center(
            child: Text(
              item.name??'',
              style: TextStyle(color: Colors.black87, fontSize: 16),
              maxLines: 1,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _areaPickerView() {
    return SizedBox(
      width: double.infinity,
      child: areas.isEmpty
          ? Container()
          : CupertinoPicker(
        scrollController: areaScrollController,
        onSelectedItemChanged: (index) {
          areaIndex = index;
          _saveInfoData();
          setState(() {});
        },
        itemExtent: 36,
        children: areas.map((item) {
          return Center(
            child: Text(
              item.name??'',
              style: TextStyle(color: Colors.black87, fontSize: 16),
              maxLines: 1,
            ),
          );
        }).toList(),
      ),
    );
  }
}

class ProvinceModelClass {
  // 省、市、区id
  int? id;
  // 省、市、区名称
  String? name;
  // 省、市、区 拼写
  String? alpha;
  // 包含的三级区域
  List<CityModelClass>? treeChild;

  ProvinceModelClass.fromMap(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    alpha = json['alpha'];
    if (json['treeChild'] != null && json['treeChild'].isNotEmpty) {
      treeChild = (json['treeChild'] as List)
          .map((e) => CityModelClass.fromMap(e))
          .toList();
    } else {
      treeChild = [];
    }
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['alpha'] = alpha;
    if (treeChild!.isEmpty) {
      data['treeChild'] = [];
    } else {
      data['treeChild'] = treeChild?.map((e) => e.toMap());
    }
    return data;
  }
}

class CityModelClass {
  // 省、市、区id
  int? id;
  // 省、市、区名称
  String? name;
  // 省、市、区 拼写
  String? alpha;
  // 包含的三级区域
  List<AreaModelClass>? treeChild;

  CityModelClass.fromMap(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    alpha = json['alpha'];
    if (json['treeChild'] != null && json['treeChild'].isNotEmpty) {
      treeChild = (json['treeChild'] as List)
          .map((e) => AreaModelClass.fromMap(e))
          .toList();
    } else {
      treeChild = [];
    }
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['alpha'] = alpha;
    if (treeChild!.isEmpty) {
      data['treeChild'] = [];
    } else {
      data['treeChild'] = treeChild?.map((e) => e.toMap());
    }
    return data;
  }
}

class AreaModelClass {
  // 省、市、区id
  int? id;
  // 省、市、区名称
  String? name;
  // 省、市、区 拼写
  String? alpha;

  AreaModelClass.fromMap(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    alpha = json['alpha'];
  }

  Map<String, dynamic> toMap() {
    Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['alpha'] = alpha;
    return data;
  }
}

class CityResult {
  String? province;
  String? provinceCode;
  String? city;
  String? cityCode;
  String? area;
  String? areaCode;
  CityResult(
      {this.province,
        this.provinceCode,
        this.city,
        this.cityCode,
        this.area,
        this.areaCode});

  CityResult.fromJson(Map<String, dynamic> json) {
    province = json['province'];
    city = json['city'];
    area = json['area'];
    provinceCode = json['provinceCode'];
    cityCode = json['cityCode'];
    areaCode = json['areaCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['province'] = province;
    data['city'] = city;
    data['area'] = area;
    data['provinceCode'] = provinceCode;
    data['cityCode'] = cityCode;
    data['areaCode'] = areaCode;

    return data;
  }
}

/// 底部选择公证处弹窗
///
///
///

class BottomAlertSearchList extends StatefulWidget {
  final Function selectValueCallBack;
  final String holderString;
  final List<String> dataSource;

  const BottomAlertSearchList({super.key,
    required this.selectValueCallBack,
    required this.dataSource,
    this.holderString = '请输入你想搜索的国家/地区'});

  @override
  State<BottomAlertSearchList> createState() => _BottomAlertSearchListState();
}

class _BottomAlertSearchListState extends State<BottomAlertSearchList>
    with WidgetsBindingObserver {
  List<String> tempData = [];

  String searchValue = '';

  List<String> saveData = [];

  /// 键盘的高度
  double keyboardHeight = 0.0;

  TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    tempData.addAll(widget.dataSource);
    saveData.addAll(widget.dataSource);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // 键盘高度
    final double viewInsetsBottom = EdgeInsets.fromWindowPadding(
        WidgetsBinding.instance.window.viewInsets,
        WidgetsBinding.instance.window.devicePixelRatio)
        .bottom;


    setState(() {
      keyboardHeight = viewInsetsBottom;
    });
  }

  // 所搜结果
  void _search(value) {
    tempData.clear();
    for (var element in saveData) {
      final pinyinStr = PinyinHelper.getPinyin(element,separator: "");
      if (element.contains(value) ||
          pinyinStr.contains(value)) {
        tempData.add(element);
      }
    }
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Spacer(),
        Container(
          padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
          decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10), topRight: Radius.circular(10))),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.all(16.0.w),
                child: Row(
                  children: [
                    Expanded(
                      child: SearchBar(
                        controller: textEditingController,
                        backgroundColor: WidgetStatePropertyAll(AppTheme.white),
                        elevation: WidgetStatePropertyAll(0),
                        shadowColor: WidgetStatePropertyAll(Colors.transparent),
                        side: WidgetStatePropertyAll(BorderSide(
                          color: AppTheme.darkGrey.withAlpha(100),
                          width: 0.5,
                        )),
                        hintText: widget.holderString,
                        hintStyle: WidgetStatePropertyAll(TextStyle(
                          color: AppTheme.darkGrey.withAlpha(100),
                          fontSize: 15,
                        )),
                        padding: WidgetStatePropertyAll(EdgeInsets.symmetric(horizontal: 15.w)),
                        autoFocus: true,
                        trailing: textEditingController.text.isEmpty ? null : [
                          IconButton(onPressed: (){
                            setState(() {
                              tempData.clear();
                              textEditingController.clear();
                              searchValue = '';
                              tempData.addAll(saveData);
                            });
                          }, icon: Icon(Icons.clear,size: 20,color: AppTheme.darkGrey.withAlpha(100),))
                        ],
                        leading: Icon(Icons.search,color: AppTheme.darkGrey.withAlpha(100),),
                        onSubmitted: (value) {
                          _search(value);
                        },
                        onChanged: (value) {
                          if (value.isEmpty) {
                            setState(() {
                              tempData.clear();
                              searchValue = '';
                              tempData.addAll(saveData);
                            });
                          }else{
                            setState(() {
                              searchValue = value;
                            });
                          }
                        },
                
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: GestureDetector(
                        onTap:searchValue.isNotEmpty ? () {
                          FocusScope.of(context).unfocus();
                          _search(searchValue);
                        } : null,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 20),
                          child: Text(
                            '搜索',
                            style: TextStyle(color: searchValue.isNotEmpty ?  Colors.blue : Colors.grey,fontSize: 17.sp),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              Container(
                constraints: BoxConstraints(
                    maxHeight: keyboardHeight == 0.0
                        ? 360
                        : keyboardHeight),
                child: MediaQuery.removeViewPadding(
                  context: context,
                  removeTop: true,
                  child: ListView.builder(
                      itemCount: tempData.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            widget.selectValueCallBack(tempData[index]);
                            AppApplication.pop();
                                                    },
                          child: Container(
                            height: 50,
                            margin: const EdgeInsets.symmetric(horizontal: 15),
                            decoration: BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        color: AppTheme.darkGrey.withAlpha(50), width: 1))),
                            child: Row(
                              children: [
                                Text(tempData[index]),
                                const Spacer(),
                              ],
                            ),
                          ),
                        );
                      }),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }
}

/// 搜索框控制类，用于控制 清除 icon（x）、取消按钮的展示 隐藏
class SearchTextController extends ChangeNotifier {
  bool _isClearShow = true;
  bool _isActionShow = false;

  bool get isClearShow => _isClearShow;

  bool get isActionShow => _isActionShow;

  /// 设置清除 icon 的展示隐藏
  set isClearShow(bool value) {
    _isClearShow = value;
    notifyListeners();
  }

  /// 设置取消按钮的展示隐藏
  set isActionShow(bool value) {
    _isActionShow = value;
    notifyListeners();
  }
}
