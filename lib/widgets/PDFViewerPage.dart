import 'dart:async';
import 'dart:io';

import 'package:bloc_test/utils/AppApplication.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:path_provider/path_provider.dart';


class PDFViewerPage extends StatefulWidget {
  final String pdfUrl;
  const PDFViewerPage({super.key, required this.pdfUrl});

  @override
  State<PDFViewerPage> createState() => _PDFViewerPageState();
}

class _PDFViewerPageState extends State<PDFViewerPage> {

  String? remotePDFPath;

  @override
  void initState() {
    super.initState();
    createFileOfPdfUrl().then((file) {
      setState(() {
        remotePDFPath = file?.path;
      });
    });
  }


  Future<File?> createFileOfPdfUrl() async {
    Completer<File> completer = Completer();
    logger.d("Start download file from internet!");
    try {
      // "https://berlin2017.droidcon.cod.newthinking.net/sites/global.droidcon.cod.newthinking.net/files/media/documents/Flutter%20-%2060FPS%20UI%20of%20the%20future%20%20-%20DroidconDE%2017.pdf";
      // final url = "https://pdfkit.org/docs/guide.pdf";
      final url = widget.pdfUrl; //"http://www.pdf995.com/samples/pdf.pdf";
      if (widget.pdfUrl.isEmpty) {
        return null;
      }
      final filename = url.substring(url.lastIndexOf("/") + 1);
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);
      var dir = await getApplicationDocumentsDirectory();
      logger.d("Download files");
      logger.d("${dir.path}/$filename");
      File file = File("${dir.path}/$filename");

      await file.writeAsBytes(bytes, flush: true);
      completer.complete(file);
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }

    return completer.future;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: remotePDFPath != null && remotePDFPath!.isNotEmpty ? PDFView(
        filePath: remotePDFPath,
      ):Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
