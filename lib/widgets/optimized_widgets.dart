import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:bloc_test/utils/appTheme.dart';

/// 优化的菜单项组件
class OptimizedMenuItem extends StatelessWidget {
  final String image;
  final String title;
  final VoidCallback onTap;
  final int index;

  const OptimizedMenuItem({
    Key? key,
    required this.image,
    required this.title,
    required this.onTap,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              OptimizedImage(
                imagePath: image,
                width: 50,
                height: 50,
              ),
              const SizedBox(height: 6),
              Text(
                title,
                style: TextStyle(
                  color: AppTheme.textBlack_3,
                  fontSize: 14.sp,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 优化的图片组件
class OptimizedImage extends StatelessWidget {
  final String imagePath;
  final double width;
  final double height;
  final BoxFit fit;
  final bool isNetwork;

  const OptimizedImage({
    Key? key,
    required this.imagePath,
    required this.width,
    required this.height,
    this.fit = BoxFit.cover,
    this.isNetwork = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (isNetwork) {
      return Image.network(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: (width * MediaQuery.of(context).devicePixelRatio).round(),
        cacheHeight: (height * MediaQuery.of(context).devicePixelRatio).round(),
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            color: AppTheme.bgB,
            child: const Icon(Icons.error, color: Colors.grey),
          );
        },
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Container(
            width: width,
            height: height,
            color: AppTheme.bgB,
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            ),
          );
        },
      );
    } else {
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        cacheWidth: (width * MediaQuery.of(context).devicePixelRatio).round(),
        cacheHeight: (height * MediaQuery.of(context).devicePixelRatio).round(),
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: width,
            height: height,
            color: AppTheme.bgB,
            child: const Icon(Icons.error, color: Colors.grey),
          );
        },
      );
    }
  }
}

/// 优化的新闻列表项组件
class OptimizedNewsItem extends StatelessWidget {
  final Map<String, dynamic> item;
  final VoidCallback onTap;

  const OptimizedNewsItem({
    Key? key,
    required this.item,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: InkWell(
        onTap: onTap,
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 0, 0, 10.w),
          padding: EdgeInsets.all(15.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(8.w)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withAlpha(10),
                offset: const Offset(0, 10),
                blurRadius: 5,
              )
            ],
          ),
          child: Row(
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: OptimizedImage(
                  imagePath: item['picture'] ?? '',
                  width: 100.w,
                  height: 100.h,
                  fit: BoxFit.fill,
                  isNetwork: true,
                ),
              ),
              const SizedBox(width: 20),
              Expanded(
                child: SizedBox(
                  height: 100.h,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          "${item['title'] ?? ''}",
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: const TextStyle(
                            fontWeight: FontWeight.w700,
                            fontSize: 16,
                            color: Color(0xff4B4B4B),
                          ),
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        "${item['notarialName'] ?? ''}",
                        style: const TextStyle(color: Color(0xff888888)),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        "${item['createDate'] ?? ''}",
                        style: const TextStyle(color: Color(0xff888888)),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

/// 优化的网格菜单组件
class OptimizedMenuGrid extends StatelessWidget {
  final List<Map<String, dynamic>> menuData;
  final Function(int) onMenuTap;

  const OptimizedMenuGrid({
    Key? key,
    required this.menuData,
    required this.onMenuTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: ColoredBox(
        color: Colors.transparent,
        child: MediaQuery.removePadding(
          context: context,
          removeBottom: true,
          removeTop: true,
          child: Padding(
            padding: EdgeInsets.only(top: 20.w),
            child: GridView.builder(
              itemCount: menuData.length,
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                mainAxisSpacing: 10,
                crossAxisSpacing: 10,
                childAspectRatio: 1,
              ),
              addAutomaticKeepAlives: false,
              addRepaintBoundaries: false, // 我们已经手动添加了RepaintBoundary
              itemBuilder: (context, index) {
                final menuItem = menuData[index];
                return OptimizedMenuItem(
                  image: menuItem['image'],
                  title: menuItem['title'],
                  index: index,
                  onTap: () => onMenuTap(index),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

/// 优化的新闻列表组件
class OptimizedNewsList extends StatelessWidget {
  final List<dynamic> newsList;
  final Function(Map<String, dynamic>) onNewsTap;

  const OptimizedNewsList({
    Key? key,
    required this.newsList,
    required this.onNewsTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (newsList.isEmpty) {
      return const SizedBox.shrink();
    }

    return RepaintBoundary(
      child: MediaQuery.removePadding(
        removeTop: true,
        context: context,
        child: ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: newsList.length,
          addAutomaticKeepAlives: false,
          addRepaintBoundaries: false, // 我们已经手动添加了RepaintBoundary
          cacheExtent: 500,
          itemBuilder: (ctx, i) {
            final item = newsList[i];
            return OptimizedNewsItem(
              item: item,
              onTap: () => onNewsTap(item),
            );
          },
        ),
      ),
    );
  }
}

/// 优化的容器组件
class OptimizedContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Decoration? decoration;
  final double? width;
  final double? height;

  const OptimizedContainer({
    Key? key,
    required this.child,
    this.padding,
    this.margin,
    this.decoration,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Container(
        padding: padding,
        margin: margin,
        decoration: decoration,
        width: width,
        height: height,
        child: child,
      ),
    );
  }
}
