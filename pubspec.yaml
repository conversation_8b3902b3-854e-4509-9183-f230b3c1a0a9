name: bloc_test
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.8
  flutter_bloc: ^9.1.1
  bloc: ^9.0.0
  meta: ^1.16.0
  pull_to_refresh: ^2.0.0
  umeng_apm_sdk: ^2.3.4
  logger: ^2.5.0
  package_info_plus: ^8.3.0
  flutter_screenutil: ^5.9.3
  url_launcher: ^6.3.1
  dio: ^5.8.0+1
  shared_preferences: ^2.5.3
  dart_sm: ^0.1.5
  flutter_easyloading: ^3.0.5
  mqtt_client: ^10.8.0
  fluttertoast: ^8.2.12
  umeng_common_sdk: ^1.2.9
  image_picker: ^1.1.2
  image_cropper: ^9.1.0
  crypto: ^3.0.3
  fluwx: ^5.6.0
  sign_in_with_apple: ^7.0.1
  common_utils: ^2.0.2
  flustars: ^2.0.1
  steel_crypt: ^3.0.0
  flutter_launcher_icons: 0.14.4
  permission_handler: ^12.0.1
  amap_flutter_location: ^3.0.0
  flutter_inappwebview: ^6.1.5
  cached_network_image: ^3.3.0
  flutter_html: ^3.0.0
  pinyin: ^3.3.0
  flutter_datetime_picker_plus: ^2.2.0
  connectivity_plus: ^6.1.4
  network_info_plus: ^6.1.4
  flutter_pdfview: ^1.4.1+1

flutter_launcher_icons:
  #  image_path: "assets/images/icon-128x128.png"
  image_path_android: "images/logo_white.png"
  image_path_ios: "images/icon-1024x1024.png"
  android: true # can specify file name here e.g. "ic_launcher"
  ios: true # can specify file name here e.g. "My-Launcher-Icon"
#  adaptive_icon_background: "assets/images/christmas-background.png" # only available for Android 8.0 devices and above
#  adaptive_icon_foreground: "assets/images/icon-foreground-432x432.png" # only available for Android 8.0 devices and above
#  adaptive_icon_foreground_inset: 16 # only available for Android 8.0 devices and above
#  adaptive_icon_monochrome: "assets/images/icon-monochrome-432x432.png" # only available for Android 13 devices and above
  min_sdk_android: 21 # android min sdk min:16, default 21
  remove_alpha_ios: true
  background_color_ios: "#ffffff"

dependency_overrides:
  http: ^0.13.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
