PODS:
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Flutter (1.0.0)
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_pdfview (1.0.2):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.4)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - network_info_plus (0.0.1):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - ReachabilitySwift (5.2.4)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - TOCropViewController (2.6.1)
  - UMAPM (2.0.4):
    - UMCommon
  - UMCommon (7.5.2):
    - UMDevice
  - UMDevice (3.4.0)
  - umeng_apm_sdk (0.0.1):
    - Flutter
    - UMAPM
  - umeng_common_sdk (0.0.1):
    - Flutter
    - UMCommon
    - UMDevice
  - url_launcher_ios (0.0.1):
    - Flutter
  - WechatOpenSDK-XCFramework (2.0.4)

DEPENDENCIES:
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - network_info_plus (from `.symlinks/plugins/network_info_plus/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - umeng_apm_sdk (from `.symlinks/plugins/umeng_apm_sdk/ios`)
  - umeng_common_sdk (from `.symlinks/plugins/umeng_common_sdk/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - AMapFoundation
    - AMapLocation
    - OrderedSet
    - ReachabilitySwift
    - TOCropViewController
    - UMAPM
    - UMCommon
    - UMDevice
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  network_info_plus:
    :path: ".symlinks/plugins/network_info_plus/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  umeng_apm_sdk:
    :path: ".symlinks/plugins/umeng_apm_sdk/ios"
  umeng_common_sdk:
    :path: ".symlinks/plugins/umeng_common_sdk/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  amap_flutter_location: f033c983c2d4319203ff7b523775579534d0d557
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  connectivity_plus: 8443422d4c5a53dee0d50779ec5dbcda1071251e
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_pdfview: 32bf27bda6fd85b9dd2c09628a824df5081246cf
  fluttertoast: 2c67e14dce98bbdb200df9e1acf610d7a6264ea1
  fluwx: 6bf9c5a3a99ad31b0de137dd92370a0d10a60f4b
  image_cropper: 655b3ba703c9e15e3111e79151624d6154288774
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  network_info_plus: cf61925ab5205dce05a4f0895989afdb6aade5fc
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sign_in_with_apple: c5dcc141574c8c54d5ac99dd2163c0c72ad22418
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  UMAPM: f038b65a3df4fe8e2df3245642d84cff7f5f63c8
  UMCommon: 72513a01ebca2dead52f2112b4d7c6196dbbe412
  UMDevice: dcdf7ec167387837559d149fbc7d793d984faf82
  umeng_apm_sdk: 62096406fedba420191f284da145e63d64a3ffcb
  umeng_common_sdk: 095b63e6f83b71548725a7c03a6c17b23be6674b
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
