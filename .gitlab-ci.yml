# 使用包含 Flutter 和相关依赖的镜像
image: cirrusci/flutter:stable

# 定义 CI/CD 阶段
stages:
  - build
  - upload

# 依赖安装作业
install_dependencies:
  stage: build
  script:
    - flutter pub get
  cache:
    paths:
      - .pub-cache/

# 构建 Android APK
build_android:
  stage: build
  script:
    - flutter build apk --release
  artifacts:
    paths:
      - build/app/outputs/flutter-apk/app-release.apk

# 构建 iOS IPA
build_ios:
  stage: build
  script:
    - flutter build ipa --release
  artifacts:
    paths:
      - build/ios/ipa/*.ipa

# 上传 Android APK 到蒲公英
upload_android_to_pugongying:
  stage: upload
  script:
    - apk_path="build/app/outputs/flutter-apk/app-release.apk"
    - response=$(curl -F "file=@$apk_path" -F "_api_key=$ed4ee22d846c095b177426b6928819f6" https://www.pgyer.com/apiv2/app/upload)
    - echo $response

# 上传 iOS IPA 到蒲公英
upload_ios_to_pugongying:
  stage: upload
  script:
    - ipa_path=$(find build/ios/ipa -name "*.ipa" | head -n 1)
    - response=$(curl -F "file=@$ipa_path" -F "_api_key=$ed4ee22d846c095b177426b6928819f6" https://www.pgyer.com/apiv2/app/upload)
    - echo $response